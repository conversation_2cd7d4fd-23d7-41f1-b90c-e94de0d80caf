"""
Order book spoofing detection module.
Identifies market manipulation through fake large orders.
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque

from ..shared.data_models import Signal, SignalType, TradeDirection, OrderBookUpdate
from ..shared.config import config


class OrderBookTracker:
    """Tracks order book changes to detect spoofing"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Track order book history for each asset
        self.order_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Track large orders and their lifetimes
        self.large_orders: Dict[str, Dict] = defaultdict(dict)  # asset -> {price_level: order_info}
        
        # Configuration
        self.size_multiplier = config.spoof_size_multiplier
        self.time_threshold = config.spoof_time_threshold
        self.volume_threshold = config.spoof_volume_threshold
        
    def process_order_book_update(self, order_book: OrderBookUpdate) -> List[Signal]:
        """Process new order book update and detect spoofing"""
        signals = []
        asset = order_book.asset
        
        # Store current order book
        self.order_history[asset].append({
            'timestamp': order_book.timestamp,
            'bids': order_book.bids.copy(),
            'asks': order_book.asks.copy()
        })
        
        # Analyze for spoofing patterns
        spoof_signals = self._detect_spoofing_patterns(asset, order_book)
        signals.extend(spoof_signals)
        
        # Update large order tracking
        self._update_large_order_tracking(asset, order_book)
        
        return signals
    
    def _detect_spoofing_patterns(self, asset: str, current_ob: OrderBookUpdate) -> List[Signal]:
        """Detect spoofing patterns in order book"""
        signals = []
        
        if len(self.order_history[asset]) < 2:
            return signals
        
        previous_ob = self.order_history[asset][-2]
        
        # Detect new large orders
        new_large_bids = self._find_new_large_orders(
            previous_ob['bids'], current_ob.bids, 'bid'
        )
        new_large_asks = self._find_new_large_orders(
            previous_ob['asks'], current_ob.asks, 'ask'
        )
        
        # Check if these are potential spoofs
        for price, size in new_large_bids:
            if self._is_potential_spoof(asset, price, size, 'bid'):
                signals.append(self._create_spoof_signal(
                    asset, price, size, 'bid', current_ob.timestamp
                ))
        
        for price, size in new_large_asks:
            if self._is_potential_spoof(asset, price, size, 'ask'):
                signals.append(self._create_spoof_signal(
                    asset, price, size, 'ask', current_ob.timestamp
                ))
        
        # Detect disappeared large orders (completed spoofs)
        disappeared_signals = self._detect_disappeared_orders(asset, previous_ob, current_ob)
        signals.extend(disappeared_signals)
        
        return signals
    
    def _find_new_large_orders(self, previous_levels: List[List[float]], 
                              current_levels: List[List[float]], 
                              side: str) -> List[Tuple[float, float]]:
        """Find new large orders that appeared"""
        new_large_orders = []
        
        # Convert to dictionaries for easier comparison
        prev_dict = {price: size for price, size in previous_levels}
        curr_dict = {price: size for price, size in current_levels}
        
        # Calculate average order size from current book
        if current_levels:
            avg_size = sum(size for _, size in current_levels) / len(current_levels)
        else:
            return new_large_orders
        
        # Find new or significantly increased orders
        for price, size in current_levels:
            prev_size = prev_dict.get(price, 0)
            size_increase = size - prev_size
            
            # Check if this is a new large order or significant increase
            if size_increase > avg_size * self.size_multiplier:
                new_large_orders.append((price, size_increase))
        
        return new_large_orders
    
    def _is_potential_spoof(self, asset: str, price: float, size: float, side: str) -> bool:
        """Check if an order has characteristics of a spoof"""
        
        # Get recent price to check if order is far from market
        if len(self.order_history[asset]) > 0:
            recent_ob = self.order_history[asset][-1]
            
            if side == 'bid' and recent_ob['bids']:
                best_bid = recent_ob['bids'][0][0]
                # Spoof bids are often placed well below market
                if price < best_bid * 0.95:  # More than 5% below best bid
                    return True
                    
            elif side == 'ask' and recent_ob['asks']:
                best_ask = recent_ob['asks'][0][0]
                # Spoof asks are often placed well above market
                if price > best_ask * 1.05:  # More than 5% above best ask
                    return True
        
        # Additional checks could include:
        # - Order size relative to recent trading volume
        # - Unusual price levels (round numbers, psychological levels)
        # - Pattern of similar orders from same source
        
        return False
    
    def _create_spoof_signal(self, asset: str, price: float, size: float, 
                           side: str, timestamp: datetime) -> Signal:
        """Create a spoofing signal"""
        
        # Determine the likely market direction impact
        # Large bid spoofs might be trying to create false support (bearish intent)
        # Large ask spoofs might be trying to create false resistance (bullish intent)
        direction = TradeDirection.SELL if side == 'bid' else TradeDirection.BUY
        
        confidence = self._calculate_spoof_confidence(size, side)
        
        return Signal(
            asset=asset,
            signal_type=SignalType.SPOOF,
            confidence=confidence,
            direction=direction,
            source=f"Spoof_Detection_{side.upper()}",
            details={
                "spoof_price": price,
                "spoof_size": size,
                "side": side,
                "detection_time": timestamp,
                "reasoning": f"Large {side} order detected with spoofing characteristics"
            }
        )
    
    def _calculate_spoof_confidence(self, size: float, side: str) -> float:
        """Calculate confidence score for spoof detection"""
        # Base confidence
        confidence = 0.4
        
        # Size factor - larger orders are more suspicious
        if size > 1000:  # Arbitrary large size threshold
            confidence += 0.2
        if size > 10000:
            confidence += 0.2
        
        # Could add more factors:
        # - Time of day (spoofing more common during low volume periods)
        # - Market volatility (more spoofing during uncertain times)
        # - Historical patterns for this asset
        
        return min(0.8, confidence)
    
    def _detect_disappeared_orders(self, asset: str, previous_ob: Dict, 
                                 current_ob: OrderBookUpdate) -> List[Signal]:
        """Detect large orders that disappeared without being filled"""
        signals = []
        
        # Check bids
        prev_bids = {price: size for price, size in previous_ob['bids']}
        curr_bids = {price: size for price, size in current_ob.bids}
        
        for price, prev_size in prev_bids.items():
            curr_size = curr_bids.get(price, 0)
            size_decrease = prev_size - curr_size
            
            # If a large order disappeared completely or mostly
            if size_decrease > prev_size * 0.8 and prev_size > 1000:  # 80% of large order gone
                # This could be a completed spoof
                signals.append(Signal(
                    asset=asset,
                    signal_type=SignalType.SPOOF,
                    confidence=0.6,
                    direction=TradeDirection.SELL,  # Spoof bid removal is bearish
                    source="Spoof_Removal_BID",
                    details={
                        "removed_price": price,
                        "removed_size": size_decrease,
                        "side": "bid",
                        "completion_time": current_ob.timestamp
                    }
                ))
        
        # Check asks
        prev_asks = {price: size for price, size in previous_ob['asks']}
        curr_asks = {price: size for price, size in current_ob.asks}
        
        for price, prev_size in prev_asks.items():
            curr_size = curr_asks.get(price, 0)
            size_decrease = prev_size - curr_size
            
            if size_decrease > prev_size * 0.8 and prev_size > 1000:
                signals.append(Signal(
                    asset=asset,
                    signal_type=SignalType.SPOOF,
                    confidence=0.6,
                    direction=TradeDirection.BUY,  # Spoof ask removal is bullish
                    source="Spoof_Removal_ASK",
                    details={
                        "removed_price": price,
                        "removed_size": size_decrease,
                        "side": "ask",
                        "completion_time": current_ob.timestamp
                    }
                ))
        
        return signals
    
    def _update_large_order_tracking(self, asset: str, order_book: OrderBookUpdate):
        """Update tracking of large orders for lifetime analysis"""
        current_time = order_book.timestamp
        
        # Clean up old tracking data
        cutoff_time = current_time - timedelta(minutes=10)
        
        # Track current large orders
        large_orders = {}
        
        # Check bids
        for price, size in order_book.bids:
            if size > 1000:  # Threshold for "large" order
                order_key = f"bid_{price}"
                if order_key not in self.large_orders[asset]:
                    # New large order
                    large_orders[order_key] = {
                        'first_seen': current_time,
                        'price': price,
                        'size': size,
                        'side': 'bid',
                        'max_size': size
                    }
                else:
                    # Update existing order
                    existing = self.large_orders[asset][order_key]
                    large_orders[order_key] = existing.copy()
                    large_orders[order_key]['size'] = size
                    large_orders[order_key]['max_size'] = max(existing['max_size'], size)
        
        # Check asks
        for price, size in order_book.asks:
            if size > 1000:
                order_key = f"ask_{price}"
                if order_key not in self.large_orders[asset]:
                    large_orders[order_key] = {
                        'first_seen': current_time,
                        'price': price,
                        'size': size,
                        'side': 'ask',
                        'max_size': size
                    }
                else:
                    existing = self.large_orders[asset][order_key]
                    large_orders[order_key] = existing.copy()
                    large_orders[order_key]['size'] = size
                    large_orders[order_key]['max_size'] = max(existing['max_size'], size)
        
        # Update tracking
        self.large_orders[asset] = large_orders
    
    def get_spoof_statistics(self, asset: str) -> Dict:
        """Get spoofing statistics for an asset"""
        if asset not in self.order_history:
            return {}
        
        recent_obs = list(self.order_history[asset])[-10:]  # Last 10 updates
        
        if not recent_obs:
            return {}
        
        # Calculate average order book depth
        avg_bid_depth = sum(len(ob['bids']) for ob in recent_obs) / len(recent_obs)
        avg_ask_depth = sum(len(ob['asks']) for ob in recent_obs) / len(recent_obs)
        
        # Calculate average order sizes
        all_bid_sizes = []
        all_ask_sizes = []
        
        for ob in recent_obs:
            all_bid_sizes.extend([size for _, size in ob['bids']])
            all_ask_sizes.extend([size for _, size in ob['asks']])
        
        avg_bid_size = sum(all_bid_sizes) / len(all_bid_sizes) if all_bid_sizes else 0
        avg_ask_size = sum(all_ask_sizes) / len(all_ask_sizes) if all_ask_sizes else 0
        
        return {
            "avg_bid_depth": avg_bid_depth,
            "avg_ask_depth": avg_ask_depth,
            "avg_bid_size": avg_bid_size,
            "avg_ask_size": avg_ask_size,
            "large_orders_tracked": len(self.large_orders.get(asset, {})),
            "observation_count": len(recent_obs)
        }
