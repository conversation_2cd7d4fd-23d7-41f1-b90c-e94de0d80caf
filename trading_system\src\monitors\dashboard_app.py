"""
Streamlit dashboard for real-time trading system monitoring.
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
import json
from typing import Dict, List

# Mock data for demonstration (in real system, this would connect to live data)
class DashboardDataProvider:
    """Provides data for the dashboard"""
    
    def __init__(self):
        self.mock_data = self._generate_mock_data()
    
    def _generate_mock_data(self):
        """Generate mock data for dashboard"""
        import random
        
        # Mock portfolio data
        portfolio = {
            "total_value": 12500.0,
            "available_cash": 2500.0,
            "total_pnl": 2500.0,
            "daily_pnl": 150.0,
            "open_positions": 3,
            "win_rate": 0.68
        }
        
        # Mock recent trades
        trades = []
        for i in range(10):
            trades.append({
                "timestamp": datetime.now() - timedelta(hours=i*2),
                "asset": random.choice(["BTCUSDT", "ETHUSDT", "BNBUSDT"]),
                "direction": random.choice(["BUY", "SELL"]),
                "quantity": round(random.uniform(0.01, 1.0), 4),
                "price": round(random.uniform(30000, 50000), 2),
                "pnl": round(random.uniform(-200, 300), 2),
                "status": "FILLED"
            })
        
        # Mock signals
        signals = []
        signal_types = ["chart", "whale", "sentiment", "spoof"]
        for i in range(20):
            signals.append({
                "timestamp": datetime.now() - timedelta(minutes=i*15),
                "asset": random.choice(["BTCUSDT", "ETHUSDT", "BNBUSDT"]),
                "type": random.choice(signal_types),
                "confidence": round(random.uniform(0.3, 0.9), 2),
                "direction": random.choice(["BUY", "SELL"]),
                "source": f"Signal_{i}"
            })
        
        # Mock market regime
        regime = {
            "current": "BREAKOUT",
            "confidence": 0.75,
            "last_update": datetime.now() - timedelta(minutes=5)
        }
        
        return {
            "portfolio": portfolio,
            "trades": trades,
            "signals": signals,
            "regime": regime
        }
    
    def get_portfolio_data(self):
        return self.mock_data["portfolio"]
    
    def get_recent_trades(self):
        return self.mock_data["trades"]
    
    def get_recent_signals(self):
        return self.mock_data["signals"]
    
    def get_market_regime(self):
        return self.mock_data["regime"]


def create_portfolio_metrics(portfolio_data: Dict):
    """Create portfolio metrics display"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Portfolio Value", 
            f"${portfolio_data['total_value']:,.2f}",
            f"${portfolio_data['daily_pnl']:+.2f}"
        )
    
    with col2:
        st.metric(
            "Total P&L",
            f"${portfolio_data['total_pnl']:+,.2f}",
            f"{(portfolio_data['total_pnl']/10000)*100:+.1f}%"
        )
    
    with col3:
        st.metric(
            "Open Positions",
            portfolio_data['open_positions'],
            "Active"
        )
    
    with col4:
        st.metric(
            "Win Rate",
            f"{portfolio_data['win_rate']*100:.1f}%",
            "Historical"
        )


def create_pnl_chart(trades_data: List[Dict]):
    """Create P&L chart"""
    df = pd.DataFrame(trades_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp')
    df['cumulative_pnl'] = df['pnl'].cumsum()
    
    fig = go.Figure()
    
    # Add cumulative P&L line
    fig.add_trace(go.Scatter(
        x=df['timestamp'],
        y=df['cumulative_pnl'],
        mode='lines+markers',
        name='Cumulative P&L',
        line=dict(color='green', width=2)
    ))
    
    # Add individual trade markers
    colors = ['green' if pnl > 0 else 'red' for pnl in df['pnl']]
    fig.add_trace(go.Scatter(
        x=df['timestamp'],
        y=df['pnl'],
        mode='markers',
        name='Individual Trades',
        marker=dict(color=colors, size=8),
        yaxis='y2'
    ))
    
    fig.update_layout(
        title="Trading Performance",
        xaxis_title="Time",
        yaxis_title="Cumulative P&L ($)",
        yaxis2=dict(
            title="Trade P&L ($)",
            overlaying='y',
            side='right'
        ),
        height=400
    )
    
    return fig


def create_signals_chart(signals_data: List[Dict]):
    """Create signals visualization"""
    df = pd.DataFrame(signals_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Count signals by type over time
    df_hourly = df.set_index('timestamp').groupby([
        pd.Grouper(freq='H'), 'type'
    ]).size().reset_index(name='count')
    
    fig = px.bar(
        df_hourly, 
        x='timestamp', 
        y='count', 
        color='type',
        title="Signal Activity by Type",
        height=300
    )
    
    return fig


def create_regime_indicator(regime_data: Dict):
    """Create market regime indicator"""
    regime_colors = {
        "RANGE": "blue",
        "BREAKOUT": "orange", 
        "TREND_UP": "green",
        "TREND_DOWN": "red",
        "HIGH_VOLATILITY": "purple"
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        ### Market Regime
        **Current:** {regime_data['current']}  
        **Confidence:** {regime_data['confidence']:.1%}  
        **Updated:** {regime_data['last_update'].strftime('%H:%M:%S')}
        """)
    
    with col2:
        # Create a simple gauge chart
        fig = go.Figure(go.Indicator(
            mode = "gauge+number",
            value = regime_data['confidence'] * 100,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Regime Confidence"},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': regime_colors.get(regime_data['current'], 'gray')},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(height=200, margin=dict(l=20, r=20, t=40, b=20))
        st.plotly_chart(fig, use_container_width=True)


def create_trades_table(trades_data: List[Dict]):
    """Create recent trades table"""
    df = pd.DataFrame(trades_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp', ascending=False)
    
    # Format for display
    df_display = df.copy()
    df_display['timestamp'] = df_display['timestamp'].dt.strftime('%H:%M:%S')
    df_display['pnl'] = df_display['pnl'].apply(lambda x: f"${x:+.2f}")
    df_display['price'] = df_display['price'].apply(lambda x: f"${x:,.2f}")
    
    st.dataframe(
        df_display[['timestamp', 'asset', 'direction', 'quantity', 'price', 'pnl', 'status']],
        use_container_width=True
    )


def create_signals_table(signals_data: List[Dict]):
    """Create recent signals table"""
    df = pd.DataFrame(signals_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp', ascending=False).head(10)
    
    # Format for display
    df_display = df.copy()
    df_display['timestamp'] = df_display['timestamp'].dt.strftime('%H:%M:%S')
    df_display['confidence'] = df_display['confidence'].apply(lambda x: f"{x:.1%}")
    
    st.dataframe(
        df_display[['timestamp', 'asset', 'type', 'direction', 'confidence', 'source']],
        use_container_width=True
    )


def main():
    """Main dashboard application"""
    st.set_page_config(
        page_title="SysCryp Trading Dashboard",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🚀 SysCryp Trading System Dashboard")
    
    # Initialize data provider
    data_provider = DashboardDataProvider()
    
    # Sidebar controls
    st.sidebar.header("System Controls")
    
    auto_refresh = st.sidebar.checkbox("Auto Refresh", value=True)
    refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 5, 60, 10)
    
    if st.sidebar.button("Manual Refresh") or auto_refresh:
        # Refresh data
        data_provider = DashboardDataProvider()
    
    # System status
    st.sidebar.markdown("### System Status")
    st.sidebar.success("🟢 System Online")
    st.sidebar.info("🔄 Data Streaming")
    st.sidebar.info("📊 Signals Active")
    
    # Main dashboard content
    
    # Portfolio Overview
    st.header("📊 Portfolio Overview")
    portfolio_data = data_provider.get_portfolio_data()
    create_portfolio_metrics(portfolio_data)
    
    # Charts section
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("💰 P&L Performance")
        trades_data = data_provider.get_recent_trades()
        pnl_chart = create_pnl_chart(trades_data)
        st.plotly_chart(pnl_chart, use_container_width=True)
    
    with col2:
        st.subheader("📡 Signal Activity")
        signals_data = data_provider.get_recent_signals()
        signals_chart = create_signals_chart(signals_data)
        st.plotly_chart(signals_chart, use_container_width=True)
    
    # Market Regime
    st.header("🎯 Market Regime Analysis")
    regime_data = data_provider.get_market_regime()
    create_regime_indicator(regime_data)
    
    # Data tables
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📋 Recent Trades")
        create_trades_table(trades_data)
    
    with col2:
        st.subheader("🔔 Recent Signals")
        create_signals_table(signals_data)
    
    # Auto-refresh functionality
    if auto_refresh:
        import time
        time.sleep(refresh_interval)
        st.experimental_rerun()


if __name__ == "__main__":
    main()
