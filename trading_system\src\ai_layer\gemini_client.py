"""
Gemini AI client for smart contract analysis and risk assessment.
"""

import asyncio
import json
import logging
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

from ..shared.data_models import ContractAnalysis
from ..shared.config import config


class GeminiClient:
    """Client for Google Gemini AI API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.initialized = False
        
        if GEMINI_AVAILABLE and config.gemini_api_key:
            try:
                genai.configure(api_key=config.gemini_api_key)
                self.model = genai.GenerativeModel(config.gemini_model)
                self.initialized = True
                self.logger.info("Gemini AI client initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Gemini client: {e}")
        else:
            self.logger.warning("Gemini AI not available - missing library or API key")
    
    async def generate_content(self, prompt: str) -> str:
        """Generate content using Gemini AI"""
        if not self.initialized:
            raise Exception("Gemini client not initialized")
        
        try:
            # Run the blocking API call in a thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                self.model.generate_content, 
                prompt
            )
            return response.text
        except Exception as e:
            self.logger.error(f"Gemini API call failed: {e}")
            raise


class ContractAnalyzer:
    """Smart contract risk analyzer using Gemini AI"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.gemini_client = GeminiClient()
        
        # Contract analysis prompt template
        self.analysis_prompt_template = """
You are an expert smart contract security auditor. Analyze the following Solidity smart contract code for security risks and potential issues.

Focus on these key areas:
1. Honeypot mechanisms (functions that prevent selling)
2. Rug pull risks (owner can drain funds, unlimited minting, etc.)
3. Centralization risks (excessive owner privileges)
4. Common vulnerabilities (reentrancy, overflow, etc.)
5. Suspicious patterns or obfuscated code

Contract Code:
```solidity
{contract_code}
```

Provide your analysis in the following JSON format:
{{
    "risk_score": <integer from 0-10, where 0 is safest and 10 is most dangerous>,
    "honeypot_risk": <boolean>,
    "rug_pull_risk": <boolean>, 
    "centralization_risk": <boolean>,
    "reasoning": "<detailed explanation of the risks found>",
    "specific_issues": [
        "<list of specific security issues found>"
    ],
    "recommendations": [
        "<list of recommendations for users>"
    ]
}}

Be thorough but concise. If the code is incomplete or you cannot determine certain risks, mention this in your reasoning.
"""
    
    async def analyze_contract_risk(self, contract_address: str, contract_source_code: str) -> ContractAnalysis:
        """Analyze smart contract for security risks"""
        
        if not contract_source_code.strip():
            return ContractAnalysis(
                contract_address=contract_address,
                risk_score=10,
                reasoning="No source code available - maximum risk assigned",
                honeypot_risk=True,
                rug_pull_risk=True,
                centralization_risk=True
            )
        
        if not self.gemini_client.initialized:
            return ContractAnalysis(
                contract_address=contract_address,
                risk_score=5,
                reasoning="AI analysis unavailable - moderate risk assigned as precaution"
            )
        
        try:
            # Prepare the prompt
            prompt = self.analysis_prompt_template.format(
                contract_code=contract_source_code[:config.max_contract_analysis_tokens]
            )
            
            # Get AI analysis
            response_text = await self.gemini_client.generate_content(prompt)
            
            # Parse the JSON response
            analysis_data = self._parse_analysis_response(response_text)
            
            return ContractAnalysis(
                contract_address=contract_address,
                risk_score=analysis_data.get('risk_score', 5),
                reasoning=analysis_data.get('reasoning', 'Analysis completed'),
                honeypot_risk=analysis_data.get('honeypot_risk', False),
                rug_pull_risk=analysis_data.get('rug_pull_risk', False),
                centralization_risk=analysis_data.get('centralization_risk', False)
            )
            
        except Exception as e:
            self.logger.error(f"Contract analysis failed for {contract_address}: {e}")
            return ContractAnalysis(
                contract_address=contract_address,
                risk_score=8,
                reasoning=f"Analysis failed due to error: {str(e)} - high risk assigned as precaution"
            )
    
    def _parse_analysis_response(self, response_text: str) -> Dict:
        """Parse the AI response and extract analysis data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response_text[start_idx:end_idx]
            analysis_data = json.loads(json_str)
            
            # Validate required fields
            if 'risk_score' not in analysis_data:
                analysis_data['risk_score'] = 5
            
            # Ensure risk score is in valid range
            risk_score = analysis_data['risk_score']
            if not isinstance(risk_score, int) or risk_score < 0 or risk_score > 10:
                analysis_data['risk_score'] = 5
            
            return analysis_data
            
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.warning(f"Failed to parse AI response as JSON: {e}")
            
            # Fallback: try to extract risk score from text
            risk_score = self._extract_risk_score_from_text(response_text)
            
            return {
                'risk_score': risk_score,
                'reasoning': f"Parsed from text response: {response_text[:500]}...",
                'honeypot_risk': 'honeypot' in response_text.lower(),
                'rug_pull_risk': 'rug pull' in response_text.lower() or 'rug-pull' in response_text.lower(),
                'centralization_risk': 'centralization' in response_text.lower() or 'centralized' in response_text.lower()
            }
    
    def _extract_risk_score_from_text(self, text: str) -> int:
        """Extract risk score from text if JSON parsing fails"""
        import re
        
        # Look for patterns like "risk score: 7" or "score of 8"
        patterns = [
            r'risk[_\s]*score[:\s]*(\d+)',
            r'score[:\s]*(\d+)',
            r'rating[:\s]*(\d+)',
            r'(\d+)[/\s]*10'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                score = int(match.group(1))
                if 0 <= score <= 10:
                    return score
        
        # Fallback based on keywords
        high_risk_keywords = ['dangerous', 'high risk', 'avoid', 'scam', 'honeypot', 'rug pull']
        medium_risk_keywords = ['caution', 'moderate', 'some risk', 'centralized']
        low_risk_keywords = ['safe', 'low risk', 'secure', 'legitimate']
        
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in high_risk_keywords):
            return 8
        elif any(keyword in text_lower for keyword in medium_risk_keywords):
            return 5
        elif any(keyword in text_lower for keyword in low_risk_keywords):
            return 2
        
        return 5  # Default moderate risk


class ContractRiskCache:
    """Cache for contract analysis results to avoid repeated API calls"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, ContractAnalysis] = {}
        self.max_size = max_size
        self.logger = logging.getLogger(__name__)
    
    def get(self, contract_address: str) -> Optional[ContractAnalysis]:
        """Get cached analysis result"""
        return self.cache.get(contract_address.lower())
    
    def set(self, contract_address: str, analysis: ContractAnalysis):
        """Cache analysis result"""
        # Simple LRU: remove oldest if at capacity
        if len(self.cache) >= self.max_size:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[contract_address.lower()] = analysis
        self.logger.debug(f"Cached analysis for contract {contract_address}")
    
    def clear(self):
        """Clear the cache"""
        self.cache.clear()
    
    def size(self) -> int:
        """Get cache size"""
        return len(self.cache)


class SmartContractAnalysisService:
    """Main service for smart contract analysis"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.analyzer = ContractAnalyzer()
        self.cache = ContractRiskCache()
    
    async def analyze_contract(self, contract_address: str, contract_source_code: str = None) -> ContractAnalysis:
        """Analyze a smart contract with caching"""
        
        # Check cache first
        cached_result = self.cache.get(contract_address)
        if cached_result:
            self.logger.debug(f"Using cached analysis for {contract_address}")
            return cached_result
        
        # If no source code provided, we'd need to fetch it
        if not contract_source_code:
            self.logger.warning(f"No source code provided for {contract_address}")
            analysis = ContractAnalysis(
                contract_address=contract_address,
                risk_score=7,
                reasoning="No source code available for analysis - assigned high risk as precaution"
            )
        else:
            # Perform analysis
            analysis = await self.analyzer.analyze_contract_risk(contract_address, contract_source_code)
        
        # Cache the result
        self.cache.set(contract_address, analysis)
        
        return analysis
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        return {
            "cache_size": self.cache.size(),
            "max_size": self.cache.max_size,
            "hit_rate": "N/A"  # Would need to track hits/misses for this
        }
