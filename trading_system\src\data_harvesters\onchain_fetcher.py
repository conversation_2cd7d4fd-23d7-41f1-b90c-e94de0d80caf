"""
On-chain data fetching module for Ethereum blockchain analysis.
Handles whale tracking, smart contract analysis, and transaction monitoring.
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from ..shared.data_models import WhaleActivity, ContractAnalysis
from ..shared.config import config


class EtherscanClient:
    """Etherscan API client for blockchain data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = config.etherscan_base_url
        self.api_key = config.etherscan_api_key
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Known whale addresses (can be expanded)
        self.whale_addresses = {
            "******************************************": "Avalanche Bridge",
            "******************************************": "Binance 14",
            "******************************************": "Binance 15",
            "******************************************": "Binance 16",
            "******************************************": "Binance Hot Wallet",
            "******************************************": "Coinbase 5",
            "******************************************": "Coinbase 6",
            "******************************************": "Coinbase 7",
        }
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    async def _make_request(self, params: Dict) -> Dict:
        """Make API request to Etherscan"""
        params['apikey'] = self.api_key
        
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        try:
            async with self.session.get(self.base_url, params=params) as response:
                data = await response.json()
                
                if data.get('status') != '1':
                    self.logger.warning(f"Etherscan API error: {data.get('message')}")
                    return {}
                    
                return data.get('result', {})
                
        except Exception as e:
            self.logger.error(f"Etherscan request failed: {e}")
            return {}
            
    async def get_latest_blocks(self, count: int = 10) -> List[Dict]:
        """Get latest blocks"""
        params = {
            'module': 'proxy',
            'action': 'eth_blockNumber'
        }
        
        latest_block_hex = await self._make_request(params)
        if not latest_block_hex:
            return []
            
        latest_block = int(latest_block_hex, 16)
        
        blocks = []
        for i in range(count):
            block_number = latest_block - i
            block_data = await self.get_block_by_number(block_number)
            if block_data:
                blocks.append(block_data)
                
        return blocks
        
    async def get_block_by_number(self, block_number: int) -> Dict:
        """Get block data by number"""
        params = {
            'module': 'proxy',
            'action': 'eth_getBlockByNumber',
            'tag': hex(block_number),
            'boolean': 'true'
        }
        
        return await self._make_request(params)
        
    async def get_contract_source(self, contract_address: str) -> str:
        """Get smart contract source code"""
        params = {
            'module': 'contract',
            'action': 'getsourcecode',
            'address': contract_address
        }
        
        result = await self._make_request(params)
        if result and len(result) > 0:
            return result[0].get('SourceCode', '')
        return ''


class WhaleTracker:
    """Tracks large wallet movements and whale activity"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.etherscan = EtherscanClient()
        self.tracked_addresses = set()
        self.whale_threshold_eth = config.whale_threshold_eth
        
    async def add_whale_address(self, address: str, label: str = ""):
        """Add address to whale tracking list"""
        self.tracked_addresses.add(address.lower())
        if label:
            self.etherscan.whale_addresses[address.lower()] = label
            
    async def scan_recent_transactions(self, hours_back: int = 1) -> List[WhaleActivity]:
        """Scan recent transactions for whale activity"""
        whale_activities = []
        
        async with self.etherscan:
            # Get recent blocks
            blocks = await self.etherscan.get_latest_blocks(count=50)
            
            for block in blocks:
                if not block or 'transactions' not in block:
                    continue
                    
                for tx in block['transactions']:
                    activity = await self._analyze_transaction(tx)
                    if activity:
                        whale_activities.append(activity)
                        
        return whale_activities
        
    async def _analyze_transaction(self, tx: Dict) -> Optional[WhaleActivity]:
        """Analyze a transaction for whale activity"""
        try:
            from_addr = tx.get('from', '').lower()
            to_addr = tx.get('to', '').lower()
            value_wei = int(tx.get('value', '0'), 16)
            value_eth = value_wei / 10**18
            
            # Check if this involves a whale address or large amount
            is_whale_addr = (from_addr in self.etherscan.whale_addresses or 
                           to_addr in self.etherscan.whale_addresses)
            is_large_amount = value_eth >= self.whale_threshold_eth
            
            if not (is_whale_addr or is_large_amount):
                return None
                
            # Determine if this is an exchange flow
            exchange_flow = self._is_exchange_address(from_addr) or self._is_exchange_address(to_addr)
            
            whale_label = (self.etherscan.whale_addresses.get(from_addr) or 
                          self.etherscan.whale_addresses.get(to_addr))
            
            return WhaleActivity(
                transaction_hash=tx['hash'],
                from_address=from_addr,
                to_address=to_addr,
                amount=value_eth,
                asset='ETH',
                timestamp=datetime.fromtimestamp(int(tx['timestamp'], 16)),
                whale_label=whale_label,
                exchange_flow=exchange_flow
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing transaction: {e}")
            return None
            
    def _is_exchange_address(self, address: str) -> bool:
        """Check if address belongs to a known exchange"""
        exchange_keywords = ['binance', 'coinbase', 'kraken', 'huobi', 'okex', 'bitfinex']
        whale_label = self.etherscan.whale_addresses.get(address.lower(), '').lower()
        return any(keyword in whale_label for keyword in exchange_keywords)


class OnChainDataFetcher:
    """Main coordinator for on-chain data fetching"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.etherscan = EtherscanClient()
        self.whale_tracker = WhaleTracker()
        self.running = False
        
    async def start_monitoring(self):
        """Start continuous on-chain monitoring"""
        self.running = True
        self.logger.info("Starting on-chain data monitoring...")
        
        while self.running:
            try:
                # Scan for whale activity every 30 seconds
                whale_activities = await self.whale_tracker.scan_recent_transactions()
                
                for activity in whale_activities:
                    self.logger.info(f"Whale activity detected: {activity.amount} ETH from {activity.whale_label}")
                    
                await asyncio.sleep(30)
                
            except Exception as e:
                self.logger.error(f"On-chain monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
                
    async def stop(self):
        """Stop on-chain monitoring"""
        self.running = False
        self.logger.info("On-chain monitoring stopped")
