"""
Market regime classification system.
Detects different market conditions to adapt trading strategies.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from ..shared.data_models import MarketRegime
from ..shared.config import config


class RegimeIndicators:
    """Calculate indicators used for regime classification"""
    
    @staticmethod
    def bollinger_band_width(prices: pd.Series, period: int = 20, std: float = 2.0) -> pd.Series:
        """Calculate Bollinger Band Width as volatility measure"""
        sma = prices.rolling(window=period).mean()
        std_dev = prices.rolling(window=period).std()
        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)
        
        # Band width as percentage of middle band
        band_width = ((upper_band - lower_band) / sma) * 100
        return band_width
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def volume_profile(volume: pd.Series, period: int = 20) -> pd.Series:
        """Calculate volume profile relative to average"""
        volume_ma = volume.rolling(window=period).mean()
        volume_ratio = volume / volume_ma
        return volume_ratio
    
    @staticmethod
    def price_momentum(prices: pd.Series, period: int = 10) -> pd.Series:
        """Calculate price momentum"""
        return (prices / prices.shift(period) - 1) * 100
    
    @staticmethod
    def trend_strength(prices: pd.Series, period: int = 20) -> pd.Series:
        """Calculate trend strength using linear regression slope"""
        def calculate_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            slope = np.polyfit(x, y, 1)[0]
            return slope / series.iloc[-1] * 100  # Normalize by price
        
        trend_strength = prices.rolling(window=period).apply(calculate_slope, raw=False)
        return trend_strength


class MarketRegimeClassifier:
    """Classifies market regimes based on multiple indicators"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Regime classification thresholds
        self.volatility_low_threshold = 2.0    # BB width < 2%
        self.volatility_high_threshold = 8.0   # BB width > 8%
        self.volume_spike_threshold = 2.0      # Volume > 2x average
        self.momentum_threshold = 3.0          # Price change > 3%
        self.trend_strength_threshold = 0.5    # Trend slope threshold
        
        # Historical data for regime analysis
        self.price_history: Dict[str, pd.DataFrame] = {}
        self.current_regime: MarketRegime = MarketRegime.RANGE
        self.regime_confidence: float = 0.5
        self.last_update: Optional[datetime] = None
        
    def update_market_data(self, asset: str, ohlcv_data: pd.DataFrame):
        """Update market data for regime analysis"""
        # Ensure we have required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in ohlcv_data.columns for col in required_cols):
            self.logger.warning(f"Missing required columns for {asset}")
            return
        
        # Store recent data (keep last 200 periods)
        self.price_history[asset] = ohlcv_data.tail(200).copy()
        
    def classify_regime(self, primary_asset: str = 'BTCUSDT') -> Tuple[MarketRegime, float]:
        """Classify current market regime"""
        
        if primary_asset not in self.price_history:
            self.logger.warning(f"No data available for {primary_asset}")
            return self.current_regime, 0.0
        
        df = self.price_history[primary_asset]
        
        if len(df) < 50:  # Need sufficient data
            return MarketRegime.RANGE, 0.3
        
        try:
            # Calculate regime indicators
            indicators = self._calculate_regime_indicators(df)
            
            # Classify regime based on indicators
            regime, confidence = self._determine_regime(indicators)
            
            # Update state
            self.current_regime = regime
            self.regime_confidence = confidence
            self.last_update = datetime.utcnow()
            
            self.logger.info(f"Market regime classified as {regime.value} with confidence {confidence:.2f}")
            
            return regime, confidence
            
        except Exception as e:
            self.logger.error(f"Regime classification failed: {e}")
            return self.current_regime, 0.0
    
    def _calculate_regime_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate all indicators needed for regime classification"""
        
        # Volatility indicators
        bb_width = RegimeIndicators.bollinger_band_width(df['close'])
        atr = RegimeIndicators.atr(df['high'], df['low'], df['close'])
        atr_normalized = (atr / df['close']) * 100  # ATR as percentage of price
        
        # Volume indicators
        volume_profile = RegimeIndicators.volume_profile(df['volume'])
        
        # Momentum indicators
        momentum_short = RegimeIndicators.price_momentum(df['close'], period=5)
        momentum_medium = RegimeIndicators.price_momentum(df['close'], period=10)
        
        # Trend indicators
        trend_strength = RegimeIndicators.trend_strength(df['close'])
        
        # Get latest values
        latest_idx = -1
        
        return {
            'bb_width': bb_width.iloc[latest_idx] if not pd.isna(bb_width.iloc[latest_idx]) else 5.0,
            'atr_pct': atr_normalized.iloc[latest_idx] if not pd.isna(atr_normalized.iloc[latest_idx]) else 2.0,
            'volume_ratio': volume_profile.iloc[latest_idx] if not pd.isna(volume_profile.iloc[latest_idx]) else 1.0,
            'momentum_short': momentum_short.iloc[latest_idx] if not pd.isna(momentum_short.iloc[latest_idx]) else 0.0,
            'momentum_medium': momentum_medium.iloc[latest_idx] if not pd.isna(momentum_medium.iloc[latest_idx]) else 0.0,
            'trend_strength': trend_strength.iloc[latest_idx] if not pd.isna(trend_strength.iloc[latest_idx]) else 0.0,
            'price_change_1h': ((df['close'].iloc[-1] / df['close'].iloc[-12]) - 1) * 100 if len(df) >= 12 else 0.0,
            'price_change_4h': ((df['close'].iloc[-1] / df['close'].iloc[-48]) - 1) * 100 if len(df) >= 48 else 0.0
        }
    
    def _determine_regime(self, indicators: Dict) -> Tuple[MarketRegime, float]:
        """Determine market regime from indicators"""
        
        bb_width = indicators['bb_width']
        atr_pct = indicators['atr_pct']
        volume_ratio = indicators['volume_ratio']
        momentum_short = indicators['momentum_short']
        momentum_medium = indicators['momentum_medium']
        trend_strength = indicators['trend_strength']
        price_change_1h = indicators['price_change_1h']
        price_change_4h = indicators['price_change_4h']
        
        # Initialize regime scores
        regime_scores = {
            MarketRegime.RANGE: 0.0,
            MarketRegime.BREAKOUT: 0.0,
            MarketRegime.TREND_UP: 0.0,
            MarketRegime.TREND_DOWN: 0.0,
            MarketRegime.HIGH_VOLATILITY: 0.0
        }
        
        # High Volatility Regime
        if bb_width > self.volatility_high_threshold or atr_pct > 5.0:
            regime_scores[MarketRegime.HIGH_VOLATILITY] += 0.4
            
        if volume_ratio > self.volume_spike_threshold:
            regime_scores[MarketRegime.HIGH_VOLATILITY] += 0.2
            regime_scores[MarketRegime.BREAKOUT] += 0.3
        
        # Range Regime (low volatility, sideways movement)
        if bb_width < self.volatility_low_threshold:
            regime_scores[MarketRegime.RANGE] += 0.4
            
        if abs(momentum_medium) < 1.0:  # Low momentum
            regime_scores[MarketRegime.RANGE] += 0.3
            
        if abs(trend_strength) < self.trend_strength_threshold:
            regime_scores[MarketRegime.RANGE] += 0.2
        
        # Breakout Regime (high volume + momentum)
        if (volume_ratio > self.volume_spike_threshold and 
            abs(momentum_short) > self.momentum_threshold):
            regime_scores[MarketRegime.BREAKOUT] += 0.5
            
        if abs(price_change_1h) > 2.0:  # Significant 1h move
            regime_scores[MarketRegime.BREAKOUT] += 0.3
        
        # Trend Regimes
        if trend_strength > self.trend_strength_threshold:
            if momentum_medium > 0:
                regime_scores[MarketRegime.TREND_UP] += 0.4
            else:
                regime_scores[MarketRegime.TREND_DOWN] += 0.4
                
        if price_change_4h > 5.0:  # Strong 4h upward move
            regime_scores[MarketRegime.TREND_UP] += 0.4
        elif price_change_4h < -5.0:  # Strong 4h downward move
            regime_scores[MarketRegime.TREND_DOWN] += 0.4
        
        # Momentum confirmation for trends
        if momentum_medium > 2.0:
            regime_scores[MarketRegime.TREND_UP] += 0.2
        elif momentum_medium < -2.0:
            regime_scores[MarketRegime.TREND_DOWN] += 0.2
        
        # Find regime with highest score
        best_regime = max(regime_scores, key=regime_scores.get)
        confidence = min(1.0, regime_scores[best_regime])
        
        # If no clear regime, default to RANGE
        if confidence < 0.3:
            return MarketRegime.RANGE, 0.3
        
        return best_regime, confidence
    
    def get_regime_characteristics(self, regime: MarketRegime) -> Dict:
        """Get characteristics and recommended strategies for a regime"""
        
        characteristics = {
            MarketRegime.RANGE: {
                "description": "Low volatility, sideways price movement",
                "volatility": "Low",
                "trend": "Sideways",
                "strategy_focus": "Mean reversion, support/resistance trading",
                "signal_weights": config.regime_weights.get("RANGE", {}),
                "risk_level": "Medium"
            },
            MarketRegime.BREAKOUT: {
                "description": "High volume with significant price movement",
                "volatility": "High",
                "trend": "Momentum-driven",
                "strategy_focus": "Momentum trading, breakout confirmation",
                "signal_weights": config.regime_weights.get("BREAKOUT", {}),
                "risk_level": "High"
            },
            MarketRegime.TREND_UP: {
                "description": "Sustained upward price movement",
                "volatility": "Medium",
                "trend": "Bullish",
                "strategy_focus": "Trend following, buy dips",
                "signal_weights": config.regime_weights.get("TREND_UP", {}),
                "risk_level": "Medium"
            },
            MarketRegime.TREND_DOWN: {
                "description": "Sustained downward price movement",
                "volatility": "Medium",
                "trend": "Bearish",
                "strategy_focus": "Short selling, sell rallies",
                "signal_weights": config.regime_weights.get("TREND_DOWN", {}),
                "risk_level": "Medium"
            },
            MarketRegime.HIGH_VOLATILITY: {
                "description": "Extreme price swings and uncertainty",
                "volatility": "Very High",
                "trend": "Chaotic",
                "strategy_focus": "Risk management, reduced position sizes",
                "signal_weights": config.regime_weights.get("HIGH_VOLATILITY", {}),
                "risk_level": "Very High"
            }
        }
        
        return characteristics.get(regime, {})
    
    def get_current_regime_info(self) -> Dict:
        """Get current regime information"""
        characteristics = self.get_regime_characteristics(self.current_regime)
        
        return {
            "regime": self.current_regime.value,
            "confidence": self.regime_confidence,
            "last_update": self.last_update,
            "characteristics": characteristics
        }
    
    def should_update_regime(self) -> bool:
        """Check if regime should be updated"""
        if self.last_update is None:
            return True
        
        # Update every 5 minutes
        time_since_update = datetime.utcnow() - self.last_update
        return time_since_update > timedelta(minutes=5)
