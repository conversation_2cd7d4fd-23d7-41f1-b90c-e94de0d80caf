"""
CEX (Centralized Exchange) data streaming module.
Handles real-time market data from Binance WebSocket streams.
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, List, Callable, Optional
from datetime import datetime

from ..shared.data_models import OrderBookUpdate
from ..shared.config import config


class BinanceStreamer:
    """Real-time Binance WebSocket data streamer"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ws_connection: Optional[websockets.WebSocketServerProtocol] = None
        self.subscribed_symbols: List[str] = []
        self.callbacks: Dict[str, List[Callable]] = {
            'ticker': [],
            'depth': [],
            'trade': [],
            'kline': []
        }
        self.running = False
        
    async def connect(self):
        """Establish WebSocket connection to Binance"""
        try:
            self.ws_connection = await websockets.connect(config.binance_ws_url)
            self.logger.info("Connected to Binance WebSocket")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to Binance WebSocket: {e}")
            return False
            
    async def subscribe_ticker(self, symbols: List[str]):
        """Subscribe to ticker updates for given symbols"""
        streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
        await self._subscribe_streams(streams)
        self.subscribed_symbols.extend(symbols)
        
    async def subscribe_depth(self, symbols: List[str], levels: int = 20):
        """Subscribe to order book depth updates"""
        streams = [f"{symbol.lower()}@depth{levels}" for symbol in symbols]
        await self._subscribe_streams(streams)
        
    async def subscribe_trades(self, symbols: List[str]):
        """Subscribe to individual trade updates"""
        streams = [f"{symbol.lower()}@trade" for symbol in symbols]
        await self._subscribe_streams(streams)
        
    async def subscribe_klines(self, symbols: List[str], interval: str = "1m"):
        """Subscribe to candlestick/kline updates"""
        streams = [f"{symbol.lower()}@kline_{interval}" for symbol in symbols]
        await self._subscribe_streams(streams)
        
    async def _subscribe_streams(self, streams: List[str]):
        """Internal method to subscribe to multiple streams"""
        if not self.ws_connection:
            await self.connect()
            
        subscribe_msg = {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }
        
        await self.ws_connection.send(json.dumps(subscribe_msg))
        self.logger.info(f"Subscribed to streams: {streams}")
        
    def add_callback(self, stream_type: str, callback: Callable):
        """Add callback function for specific stream type"""
        if stream_type in self.callbacks:
            self.callbacks[stream_type].append(callback)
            
    async def start_streaming(self):
        """Start the main streaming loop"""
        if not self.ws_connection:
            if not await self.connect():
                return
                
        self.running = True
        self.logger.info("Starting Binance data streaming...")
        
        try:
            while self.running:
                message = await self.ws_connection.recv()
                await self._process_message(json.loads(message))
                
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
        except Exception as e:
            self.logger.error(f"Streaming error: {e}")
        finally:
            self.running = False
            
    async def _process_message(self, data: Dict):
        """Process incoming WebSocket message"""
        if 'stream' not in data:
            return
            
        stream = data['stream']
        event_data = data['data']
        
        # Route to appropriate handler
        if '@ticker' in stream:
            await self._handle_ticker(event_data)
        elif '@depth' in stream:
            await self._handle_depth(event_data)
        elif '@trade' in stream:
            await self._handle_trade(event_data)
        elif '@kline' in stream:
            await self._handle_kline(event_data)
            
    async def _handle_ticker(self, data: Dict):
        """Handle ticker price updates"""
        ticker_data = {
            'symbol': data['s'],
            'price': float(data['c']),
            'change': float(data['P']),
            'volume': float(data['v']),
            'timestamp': datetime.fromtimestamp(data['E'] / 1000)
        }
        
        # Call registered callbacks
        for callback in self.callbacks['ticker']:
            try:
                await callback(ticker_data)
            except Exception as e:
                self.logger.error(f"Ticker callback error: {e}")
                
    async def _handle_depth(self, data: Dict):
        """Handle order book depth updates"""
        order_book = OrderBookUpdate(
            asset=data['s'],
            timestamp=datetime.fromtimestamp(data['E'] / 1000),
            bids=[[float(bid[0]), float(bid[1])] for bid in data['b']],
            asks=[[float(ask[0]), float(ask[1])] for ask in data['a']]
        )
        
        # Call registered callbacks
        for callback in self.callbacks['depth']:
            try:
                await callback(order_book)
            except Exception as e:
                self.logger.error(f"Depth callback error: {e}")
                
    async def _handle_trade(self, data: Dict):
        """Handle individual trade updates"""
        trade_data = {
            'symbol': data['s'],
            'price': float(data['p']),
            'quantity': float(data['q']),
            'timestamp': datetime.fromtimestamp(data['T'] / 1000),
            'is_buyer_maker': data['m']
        }
        
        # Call registered callbacks
        for callback in self.callbacks['trade']:
            try:
                await callback(trade_data)
            except Exception as e:
                self.logger.error(f"Trade callback error: {e}")
                
    async def _handle_kline(self, data: Dict):
        """Handle candlestick/kline updates"""
        kline = data['k']
        kline_data = {
            'symbol': kline['s'],
            'open': float(kline['o']),
            'high': float(kline['h']),
            'low': float(kline['l']),
            'close': float(kline['c']),
            'volume': float(kline['v']),
            'timestamp': datetime.fromtimestamp(kline['t'] / 1000),
            'is_closed': kline['x']  # True if this kline is closed
        }
        
        # Call registered callbacks
        for callback in self.callbacks['kline']:
            try:
                await callback(kline_data)
            except Exception as e:
                self.logger.error(f"Kline callback error: {e}")
                
    async def stop(self):
        """Stop the streaming and close connection"""
        self.running = False
        if self.ws_connection:
            await self.ws_connection.close()
        self.logger.info("Binance streamer stopped")


class DataHarvester:
    """Main data harvesting coordinator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.binance_streamer = BinanceStreamer()
        self.running = False
        
    async def start(self, symbols: List[str]):
        """Start data harvesting for specified symbols"""
        self.logger.info(f"Starting data harvesting for symbols: {symbols}")
        
        # Subscribe to various data streams
        await self.binance_streamer.subscribe_ticker(symbols)
        await self.binance_streamer.subscribe_depth(symbols)
        await self.binance_streamer.subscribe_trades(symbols)
        await self.binance_streamer.subscribe_klines(symbols)
        
        # Start streaming
        self.running = True
        await self.binance_streamer.start_streaming()
        
    async def stop(self):
        """Stop data harvesting"""
        self.running = False
        await self.binance_streamer.stop()
        self.logger.info("Data harvesting stopped")
        
    def add_ticker_callback(self, callback: Callable):
        """Add callback for ticker updates"""
        self.binance_streamer.add_callback('ticker', callback)
        
    def add_depth_callback(self, callback: Callable):
        """Add callback for order book updates"""
        self.binance_streamer.add_callback('depth', callback)
        
    def add_trade_callback(self, callback: Callable):
        """Add callback for trade updates"""
        self.binance_streamer.add_callback('trade', callback)
        
    def add_kline_callback(self, callback: Callable):
        """Add callback for kline updates"""
        self.binance_streamer.add_callback('kline', callback)
