"""
Risk management system for position sizing and portfolio risk control.
"""

import logging
import math
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from ..shared.data_models import (
    ProposedTrade, SizedTradeOrder, ExecutedTrade, PortfolioState, 
    TradeDirection, TradeStatus, MarketRegime
)
from ..shared.config import config


class PositionSizer:
    """Calculates position sizes based on risk parameters"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def calculate_position_size(self, 
                              proposed_trade: ProposedTrade,
                              portfolio_value: float,
                              current_price: float,
                              stop_loss_price: Optional[float] = None) -> Tuple[float, float]:
        """Calculate position size and risk amount"""
        
        # Base risk per trade (percentage of portfolio)
        base_risk_pct = config.risk_per_trade_percent / 100
        
        # Adjust risk based on confidence
        confidence_multiplier = 0.5 + (proposed_trade.confidence * 0.5)  # 0.5x to 1.0x
        
        # Adjust risk based on market regime
        regime_multiplier = self._get_regime_risk_multiplier(proposed_trade.market_regime)
        
        # Calculate final risk percentage
        final_risk_pct = base_risk_pct * confidence_multiplier * regime_multiplier
        
        # Calculate risk amount in USD
        risk_amount = portfolio_value * final_risk_pct
        
        # Ensure we don't exceed maximum position size
        max_position_value = min(
            config.max_position_size_usd,
            portfolio_value * 0.2  # Max 20% of portfolio in single position
        )
        
        if stop_loss_price:
            # Calculate position size based on stop loss
            price_diff = abs(current_price - stop_loss_price)
            risk_per_unit = price_diff
            
            if risk_per_unit > 0:
                quantity = risk_amount / risk_per_unit
                position_value = quantity * current_price
                
                # Limit position value
                if position_value > max_position_value:
                    quantity = max_position_value / current_price
                    risk_amount = quantity * risk_per_unit
            else:
                # Fallback if stop loss calculation fails
                quantity = max_position_value / current_price
                risk_amount = quantity * current_price * 0.02  # 2% risk
        else:
            # No stop loss - use fixed percentage of portfolio
            position_value = min(risk_amount * 10, max_position_value)  # 10:1 leverage assumption
            quantity = position_value / current_price
            risk_amount = position_value * 0.02  # 2% risk without stop loss
        
        return quantity, risk_amount
    
    def _get_regime_risk_multiplier(self, regime: MarketRegime) -> float:
        """Get risk multiplier based on market regime"""
        multipliers = {
            MarketRegime.RANGE: 1.0,           # Normal risk
            MarketRegime.BREAKOUT: 1.2,        # Slightly higher risk for momentum
            MarketRegime.TREND_UP: 1.1,        # Slightly higher risk in trends
            MarketRegime.TREND_DOWN: 0.9,      # Lower risk in downtrends
            MarketRegime.HIGH_VOLATILITY: 0.6  # Much lower risk in volatile markets
        }
        
        return multipliers.get(regime, 1.0)


class RiskManager:
    """Main risk management coordinator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.position_sizer = PositionSizer()
        
        # Mock portfolio state (in real system, this would be fetched from database)
        self.portfolio_state = PortfolioState(
            total_value=10000.0,  # $10k starting portfolio
            available_cash=10000.0,
            open_positions=[]
        )
        
    async def validate_and_size(self, proposed_trade: ProposedTrade) -> Optional[SizedTradeOrder]:
        """Validate proposed trade and calculate position size"""
        
        try:
            # Get current price (mock - in real system, fetch from market data)
            current_price = await self._get_current_price(proposed_trade.asset)
            
            if not current_price:
                self.logger.error(f"Could not get current price for {proposed_trade.asset}")
                return None
            
            # Check basic constraints
            if len(self.portfolio_state.open_positions) >= config.max_open_positions:
                self.logger.warning(f"Maximum positions limit reached")
                return None
            
            # Calculate stop loss price
            stop_loss_price = self._calculate_stop_loss(current_price, proposed_trade.direction)
            
            # Calculate position size
            quantity, risk_amount = self.position_sizer.calculate_position_size(
                proposed_trade, 
                self.portfolio_state.total_value,
                current_price,
                stop_loss_price
            )
            
            # Calculate take profit
            take_profit_price = self._calculate_take_profit(current_price, proposed_trade.direction, proposed_trade.confidence)
            
            # Create sized order
            sized_order = SizedTradeOrder(
                asset=proposed_trade.asset,
                direction=proposed_trade.direction,
                quantity=quantity,
                price=current_price,  # Market order
                stop_loss=stop_loss_price,
                take_profit=take_profit_price,
                confidence=proposed_trade.confidence,
                risk_amount=risk_amount,
                order_type="MARKET"
            )
            
            self.logger.info(f"Sized order created: {quantity:.6f} {proposed_trade.asset} at ${current_price:.2f}, risk: ${risk_amount:.2f}")
            
            return sized_order
            
        except Exception as e:
            self.logger.error(f"Risk management failed for {proposed_trade.asset}: {e}")
            return None
    
    async def _get_current_price(self, asset: str) -> Optional[float]:
        """Get current market price for asset"""
        # Mock implementation - in real system, fetch from market data feed
        mock_prices = {
            'BTCUSDT': 45000.0,
            'ETHUSDT': 3000.0,
            'BNBUSDT': 400.0,
            'ADAUSDT': 0.5,
            'SOLUSDT': 100.0
        }
        
        return mock_prices.get(asset.upper(), 100.0)  # Default price
    
    def _calculate_stop_loss(self, current_price: float, direction: TradeDirection) -> float:
        """Calculate stop loss price"""
        stop_loss_pct = 0.02  # 2% stop loss
        
        if direction == TradeDirection.BUY:
            return current_price * (1 - stop_loss_pct)
        else:
            return current_price * (1 + stop_loss_pct)
    
    def _calculate_take_profit(self, current_price: float, direction: TradeDirection, confidence: float) -> float:
        """Calculate take profit price based on confidence"""
        # Higher confidence = higher take profit target
        base_tp_pct = 0.03  # 3% base take profit
        confidence_multiplier = 1 + confidence  # 1x to 2x
        tp_pct = base_tp_pct * confidence_multiplier
        
        if direction == TradeDirection.BUY:
            return current_price * (1 + tp_pct)
        else:
            return current_price * (1 - tp_pct)
    
    def update_portfolio_state(self, executed_trade: ExecutedTrade):
        """Update portfolio state after trade execution"""
        if executed_trade.status == TradeStatus.FILLED:
            # Add to open positions
            self.portfolio_state.open_positions.append(executed_trade)
            
            # Update available cash
            trade_value = executed_trade.quantity * executed_trade.entry_price
            if executed_trade.direction == TradeDirection.BUY:
                self.portfolio_state.available_cash -= trade_value
            else:
                self.portfolio_state.available_cash += trade_value
    
    def get_portfolio_summary(self) -> Dict:
        """Get portfolio summary"""
        total_pnl = sum(pos.pnl or 0 for pos in self.portfolio_state.open_positions)
        
        return {
            "total_value": self.portfolio_state.total_value,
            "available_cash": self.portfolio_state.available_cash,
            "open_positions": len(self.portfolio_state.open_positions),
            "total_pnl": total_pnl,
            "risk_utilization": len(self.portfolio_state.open_positions) / config.max_open_positions
        }
