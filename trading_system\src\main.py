"""
Main entry point for the SysCryp Trading System.
"""

import asyncio
import logging
import signal
import sys
from typing import Dict, Any

from shared.config import config, validate_config
from shared.data_models import SystemHealth


class TradingSystem:
    """Main trading system orchestrator"""
    
    def __init__(self):
        self.running = False
        self.components: Dict[str, Any] = {}
        self.health = SystemHealth(uptime_seconds=0.0)
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    async def initialize_components(self):
        """Initialize all system components"""
        self.logger.info("Initializing trading system components...")
        
        # TODO: Initialize components as they are implemented
        # self.components['data_harvester'] = DataHarvester()
        # self.components['signal_generator'] = SignalGenerator()
        # self.components['ai_enricher'] = AIEnricher()
        # self.components['regime_classifier'] = RegimeClassifier()
        # self.components['decision_engine'] = DecisionEngine()
        # self.components['risk_manager'] = RiskManager()
        # self.components['execution_handler'] = ExecutionHandler()
        # self.components['system_monitor'] = SystemMonitor()
        
        self.logger.info("All components initialized successfully")
        
    async def start(self):
        """Start the trading system"""
        if not validate_config():
            self.logger.error("Configuration validation failed")
            return False
            
        self.logger.info("Starting SysCryp Trading System...")
        
        try:
            await self.initialize_components()
            self.running = True
            
            # Main event loop
            while self.running:
                await asyncio.sleep(1)
                # TODO: Add main system logic here
                
        except Exception as e:
            self.logger.error(f"System error: {e}")
            return False
            
        return True
        
    async def stop(self):
        """Gracefully stop the trading system"""
        self.logger.info("Stopping trading system...")
        self.running = False
        
        # TODO: Cleanup components
        for name, component in self.components.items():
            if hasattr(component, 'stop'):
                await component.stop()
                
        self.logger.info("Trading system stopped")


async def main():
    """Main entry point"""
    system = TradingSystem()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        asyncio.create_task(system.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the system
    success = await system.start()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
