"""
Main entry point for the SysCryp Trading System.
"""

import asyncio
import logging
import signal
import sys
from typing import Dict, Any
from datetime import datetime

from shared.config import config, validate_config
from shared.data_models import SystemHealth, Signal

# Import all components
from data_harvesters import DataHarvester, OnChainDataFetcher, SocialDataAggregator
from signal_generators import ChartSignalGenerator, WhaleSignalGenerator, OrderBookTracker
from ai_layer import SmartContractAnalysisService, SentimentAnalysisService
from engine import DecisionEngine, MarketRegimeClassifier, RiskManager
from execution import ExecutionHandler
from monitors import alert_manager


class TradingSystem:
    """Main trading system orchestrator"""

    def __init__(self):
        self.running = False
        self.components: Dict[str, Any] = {}
        self.health = SystemHealth(uptime_seconds=0.0)
        self.start_time = datetime.utcnow()

        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    async def initialize_components(self):
        """Initialize all system components"""
        self.logger.info("Initializing trading system components...")

        try:
            # Initialize core components
            self.components['risk_manager'] = RiskManager()
            self.components['execution_handler'] = ExecutionHandler(use_mock=True)  # Use mock for safety
            self.components['regime_classifier'] = MarketRegimeClassifier()

            # Initialize decision engine with dependencies
            self.components['decision_engine'] = DecisionEngine(
                risk_manager=self.components['risk_manager'],
                execution_handler=self.components['execution_handler']
            )

            # Initialize signal generators
            self.components['chart_generator'] = ChartSignalGenerator()
            self.components['whale_generator'] = WhaleSignalGenerator()
            self.components['spoof_detector'] = OrderBookTracker()

            # Initialize data harvesters
            self.components['market_data'] = DataHarvester()
            self.components['onchain_data'] = OnChainDataFetcher()
            self.components['social_data'] = SocialDataAggregator()

            # Initialize AI services
            self.components['contract_analyzer'] = SmartContractAnalysisService()
            self.components['sentiment_analyzer'] = SentimentAnalysisService()

            # Setup callbacks
            self._setup_callbacks()

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            raise

    def _setup_callbacks(self):
        """Setup callbacks between components"""

        # Setup trade callbacks
        decision_engine = self.components['decision_engine']
        execution_handler = self.components['execution_handler']

        # Alert manager callbacks
        decision_engine.add_trade_callback(alert_manager.handle_proposed_trade)
        execution_handler.add_trade_callback(alert_manager.handle_trade_executed)

        # Market data callbacks (would be setup when data streams are active)
        # self.components['market_data'].add_ticker_callback(self._handle_price_update)
        # self.components['market_data'].add_depth_callback(self._handle_orderbook_update)

    async def _handle_signal(self, signal: Signal):
        """Handle new signal from any source"""
        try:
            await self.components['decision_engine'].process_signal(signal)
            await alert_manager.handle_signal_generated(signal)
        except Exception as e:
            self.logger.error(f"Error handling signal: {e}")

    async def start(self):
        """Start the trading system"""
        if not validate_config():
            self.logger.error("Configuration validation failed")
            return False

        self.logger.info("Starting SysCryp Trading System...")

        try:
            await self.initialize_components()

            # Send startup notification
            await alert_manager.system_startup()

            self.running = True

            # Start background tasks
            tasks = [
                self._main_loop(),
                self._health_monitor(),
                self.components['execution_handler'].monitor_orders()
            ]

            # Run all tasks concurrently
            await asyncio.gather(*tasks)

        except Exception as e:
            self.logger.error(f"System error: {e}")
            return False

        return True

    async def _main_loop(self):
        """Main system loop"""
        self.logger.info("Main system loop started")

        # Track assets to monitor
        monitored_assets = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']

        while self.running:
            try:
                # Update market regime periodically
                regime_classifier = self.components['regime_classifier']
                if regime_classifier.should_update_regime():
                    old_regime = regime_classifier.current_regime
                    new_regime, confidence = regime_classifier.classify_regime()

                    if new_regime != old_regime:
                        await alert_manager.handle_regime_change(old_regime, new_regime, confidence)

                # In a real system, this would process incoming data streams
                # For now, we'll just maintain the event loop
                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Main loop error: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _health_monitor(self):
        """Monitor system health"""
        while self.running:
            try:
                # Update health metrics
                uptime = (datetime.utcnow() - self.start_time).total_seconds()
                self.health.uptime_seconds = uptime
                self.health.last_heartbeat = datetime.utcnow()

                # Check component health
                active_connections = {}
                for name, component in self.components.items():
                    if hasattr(component, 'running'):
                        active_connections[name] = getattr(component, 'running', False)
                    else:
                        active_connections[name] = True  # Assume healthy if no running attribute

                self.health.active_connections = active_connections

                # Log health status periodically
                if int(uptime) % 300 == 0:  # Every 5 minutes
                    self.logger.info(f"System health: uptime={uptime:.0f}s, components={len(active_connections)}")

                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(30)

    async def stop(self):
        """Gracefully stop the trading system"""
        self.logger.info("Stopping trading system...")
        self.running = False

        # Send shutdown notification
        await alert_manager.system_shutdown()

        # Stop components
        for name, component in self.components.items():
            try:
                if hasattr(component, 'stop'):
                    await component.stop()
                    self.logger.info(f"Stopped component: {name}")
            except Exception as e:
                self.logger.error(f"Error stopping {name}: {e}")

        self.logger.info("Trading system stopped")

    def get_system_status(self) -> Dict:
        """Get current system status"""
        return {
            "running": self.running,
            "uptime_seconds": self.health.uptime_seconds,
            "components": list(self.components.keys()),
            "health": self.health.dict()
        }


async def main():
    """Main entry point"""
    system = TradingSystem()

    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        asyncio.create_task(system.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Start the system
    success = await system.start()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
