"""
Core data models for the trading system using Pydantic for type safety and validation.
"""

from datetime import datetime
from typing import Dict, List, Literal, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class MarketRegime(str, Enum):
    """Market regime classifications"""
    RANGE = "RANGE"
    BREAKOUT = "BREAKOUT"
    TREND_UP = "TREND_UP"
    TREND_DOWN = "TREND_DOWN"
    HIGH_VOLATILITY = "HIGH_VOLATILITY"


class SignalType(str, Enum):
    """Types of trading signals"""
    WHALE = "whale"
    CHART = "chart"
    SENTIMENT = "sentiment"
    SPOOF = "spoof"
    VOLUME = "volume"
    MOMENTUM = "momentum"


class TradeDirection(str, Enum):
    """Trade direction"""
    BUY = "BUY"
    SELL = "SELL"


class TradeStatus(str, Enum):
    """Trade execution status"""
    PENDING = "PENDING"
    FILLED = "FILLED"
    PARTIAL = "PARTIAL"
    CANCELLED = "CANCELLED"
    FAILED = "FAILED"


class Signal(BaseModel):
    """Individual trading signal"""
    asset: str = Field(..., description="Asset symbol (e.g., BTCUSDT)")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    signal_type: SignalType
    confidence: float = Field(..., ge=0.0, le=1.0, description="Signal confidence 0-1")
    direction: TradeDirection
    details: Dict[str, Any] = Field(default_factory=dict)
    source: str = Field(..., description="Source of the signal")
    
    @validator('asset')
    def validate_asset(cls, v):
        return v.upper()


class ContractAnalysis(BaseModel):
    """Smart contract risk analysis result"""
    contract_address: str
    risk_score: int = Field(..., ge=0, le=10, description="Risk score 0-10 (0=safe, 10=dangerous)")
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    honeypot_risk: bool = False
    rug_pull_risk: bool = False
    centralization_risk: bool = False


class SentimentAnalysis(BaseModel):
    """Sentiment analysis result"""
    asset: str
    sentiment_score: float = Field(..., ge=-1.0, le=1.0, description="Sentiment -1 to 1")
    confidence: float = Field(..., ge=0.0, le=1.0)
    source_count: int = Field(..., ge=0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    keywords: List[str] = Field(default_factory=list)


class WhaleActivity(BaseModel):
    """Whale transaction activity"""
    transaction_hash: str
    from_address: str
    to_address: str
    amount: float
    asset: str
    timestamp: datetime
    whale_label: Optional[str] = None
    exchange_flow: bool = False  # True if to/from exchange
    
    
class OrderBookUpdate(BaseModel):
    """Order book update data"""
    asset: str
    timestamp: datetime
    bids: List[List[float]]  # [[price, quantity], ...]
    asks: List[List[float]]  # [[price, quantity], ...]
    
    
class ProposedTrade(BaseModel):
    """A proposed trade before risk management"""
    asset: str
    direction: TradeDirection
    confidence: float = Field(..., ge=0.0, le=1.0)
    signals: List[Signal]
    market_regime: MarketRegime
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    reasoning: str = ""


class SizedTradeOrder(BaseModel):
    """A trade order with position sizing applied"""
    asset: str
    direction: TradeDirection
    quantity: float = Field(..., gt=0)
    price: Optional[float] = None  # None for market orders
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = Field(..., ge=0.0, le=1.0)
    risk_amount: float = Field(..., gt=0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    order_type: Literal["MARKET", "LIMIT"] = "MARKET"


class ExecutedTrade(BaseModel):
    """A completed trade execution"""
    trade_id: str
    asset: str
    direction: TradeDirection
    quantity: float
    entry_price: float
    exit_price: Optional[float] = None
    status: TradeStatus
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    exit_timestamp: Optional[datetime] = None
    pnl: Optional[float] = None
    fees: float = 0.0
    
    
class PortfolioState(BaseModel):
    """Current portfolio state"""
    total_value: float
    available_cash: float
    open_positions: List[ExecutedTrade]
    daily_pnl: float = 0.0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class SystemHealth(BaseModel):
    """System health metrics"""
    uptime_seconds: float
    signals_processed: int = 0
    trades_executed: int = 0
    api_errors: int = 0
    last_heartbeat: datetime = Field(default_factory=datetime.utcnow)
    active_connections: Dict[str, bool] = Field(default_factory=dict)
