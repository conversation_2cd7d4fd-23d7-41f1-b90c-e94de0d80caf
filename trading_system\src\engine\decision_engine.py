"""
Decision engine that combines signals with regime-aware strategies.
Core brain of the trading system.
"""

import asyncio
import logging
from typing import List, Dict, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque

from ..shared.data_models import (
    Signal, ProposedTrade, MarketRegime, SignalType, TradeDirection,
    ContractAnalysis, SentimentAnalysis
)
from ..shared.config import config, get_signal_weights
from .regime_classifier import MarketRegimeClassifier
from ..ai_layer import SmartContractAnalysisService, SentimentAnalysisService


class SignalAggregator:
    """Aggregates and manages signals from multiple sources"""
    
    def __init__(self, window_minutes: int = 30):
        self.logger = logging.getLogger(__name__)
        self.window_minutes = window_minutes
        
        # Store signals by asset
        self.signals: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
    def add_signal(self, signal: Signal):
        """Add a new signal"""
        asset = signal.asset.upper()
        self.signals[asset].append(signal)
        
        # Clean old signals
        self._clean_old_signals(asset)
        
    def get_recent_signals(self, asset: str, minutes_back: int = None) -> List[Signal]:
        """Get recent signals for an asset"""
        if minutes_back is None:
            minutes_back = self.window_minutes
            
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes_back)
        asset = asset.upper()
        
        return [
            signal for signal in self.signals[asset]
            if signal.timestamp > cutoff_time
        ]
    
    def get_signals_by_type(self, asset: str, signal_type: SignalType, minutes_back: int = None) -> List[Signal]:
        """Get recent signals of specific type"""
        recent_signals = self.get_recent_signals(asset, minutes_back)
        return [s for s in recent_signals if s.signal_type == signal_type]
    
    def _clean_old_signals(self, asset: str):
        """Remove signals older than the window"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.window_minutes * 2)  # Keep 2x window
        
        while (self.signals[asset] and 
               self.signals[asset][0].timestamp < cutoff_time):
            self.signals[asset].popleft()


class DecisionEngine:
    """Main decision engine for trading signals"""
    
    def __init__(self, risk_manager=None, execution_handler=None):
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.signal_aggregator = SignalAggregator()
        self.regime_classifier = MarketRegimeClassifier()
        self.contract_analyzer = SmartContractAnalysisService()
        self.sentiment_analyzer = SentimentAnalysisService()
        
        # External components (injected)
        self.risk_manager = risk_manager
        self.execution_handler = execution_handler
        
        # Configuration
        self.entry_threshold = config.entry_confidence_threshold
        self.contract_risk_threshold = 5  # Max acceptable contract risk score
        
        # Callbacks for trade decisions
        self.trade_callbacks: List[Callable] = []
        
        # Track recent decisions to avoid spam
        self.recent_decisions: Dict[str, datetime] = {}
        self.decision_cooldown_minutes = 10
        
    def add_trade_callback(self, callback: Callable):
        """Add callback for trade decisions"""
        self.trade_callbacks.append(callback)
        
    async def process_signal(self, signal: Signal):
        """Process a new signal and potentially generate trade decision"""
        
        # Add signal to aggregator
        self.signal_aggregator.add_signal(signal)
        
        # Check if we should make a decision for this asset
        if not self._should_make_decision(signal.asset):
            return
        
        try:
            # Analyze the asset and make decision
            proposed_trade = await self._analyze_and_decide(signal.asset)
            
            if proposed_trade:
                # Record decision time
                self.recent_decisions[signal.asset] = datetime.utcnow()
                
                # Process the trade through risk management and execution
                await self._process_proposed_trade(proposed_trade)
                
        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.asset}: {e}")
    
    async def _analyze_and_decide(self, asset: str) -> Optional[ProposedTrade]:
        """Analyze all available data for an asset and make trading decision"""
        
        # Get recent signals
        recent_signals = self.signal_aggregator.get_recent_signals(asset, minutes_back=30)
        
        if not recent_signals:
            return None
        
        # Get current market regime
        current_regime, regime_confidence = self.regime_classifier.classify_regime()
        
        # Perform contract risk analysis (if applicable)
        contract_risk_score = await self._assess_contract_risk(asset)
        
        if contract_risk_score > self.contract_risk_threshold:
            self.logger.warning(f"High contract risk for {asset} (score: {contract_risk_score}). Skipping.")
            return None
        
        # Analyze sentiment
        sentiment_score = await self._assess_sentiment(asset)
        
        # Calculate combined confidence score
        final_confidence = self._calculate_combined_confidence(
            recent_signals, current_regime, sentiment_score
        )
        
        # Determine trade direction
        trade_direction = self._determine_trade_direction(recent_signals, current_regime)
        
        # Check if confidence meets threshold
        if final_confidence < self.entry_threshold:
            self.logger.debug(f"Confidence {final_confidence:.2f} below threshold {self.entry_threshold} for {asset}")
            return None
        
        # Create proposed trade
        proposed_trade = ProposedTrade(
            asset=asset,
            direction=trade_direction,
            confidence=final_confidence,
            signals=recent_signals,
            market_regime=current_regime,
            reasoning=self._generate_reasoning(recent_signals, current_regime, sentiment_score, final_confidence)
        )
        
        self.logger.info(f"Proposed {trade_direction.value} trade for {asset} with confidence {final_confidence:.2f}")
        
        return proposed_trade
    
    async def _assess_contract_risk(self, asset: str) -> float:
        """Assess smart contract risk for the asset"""
        try:
            # For now, we'll assume most major assets are safe
            # In a real implementation, you'd fetch contract addresses and analyze them
            
            major_assets = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'}
            
            if asset.upper() in major_assets:
                return 1.0  # Low risk for major assets
            
            # For other assets, we'd need to:
            # 1. Get contract address from asset symbol
            # 2. Fetch contract source code
            # 3. Analyze with AI
            
            # Placeholder: assign moderate risk to unknown assets
            return 4.0
            
        except Exception as e:
            self.logger.error(f"Contract risk assessment failed for {asset}: {e}")
            return 8.0  # High risk on error
    
    async def _assess_sentiment(self, asset: str) -> float:
        """Assess sentiment for the asset"""
        try:
            # In a real implementation, this would fetch recent social media data
            # and analyze sentiment. For now, return neutral sentiment.
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Sentiment assessment failed for {asset}: {e}")
            return 0.0
    
    def _calculate_combined_confidence(self, signals: List[Signal], regime: MarketRegime, sentiment: float) -> float:
        """Calculate combined confidence score from all signals"""
        
        if not signals:
            return 0.0
        
        # Get regime-specific weights
        weights = get_signal_weights(regime.value)
        
        # Group signals by type
        signal_groups = defaultdict(list)
        for signal in signals:
            signal_groups[signal.signal_type.value].append(signal)
        
        # Calculate weighted confidence for each signal type
        type_confidences = {}
        
        for signal_type, type_signals in signal_groups.items():
            if signal_type in weights:
                # Average confidence for this signal type
                avg_confidence = sum(s.confidence for s in type_signals) / len(type_signals)
                type_confidences[signal_type] = avg_confidence
        
        # Calculate weighted final confidence
        total_weight = 0
        weighted_sum = 0
        
        for signal_type, confidence in type_confidences.items():
            weight = weights.get(signal_type, 0)
            weighted_sum += confidence * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        base_confidence = weighted_sum / total_weight
        
        # Apply sentiment modifier (small influence)
        sentiment_modifier = 1.0 + (sentiment * 0.1)  # ±10% based on sentiment
        
        # Apply regime confidence modifier
        regime_modifier = 0.8 + (self.regime_classifier.regime_confidence * 0.4)  # 0.8 to 1.2
        
        final_confidence = base_confidence * sentiment_modifier * regime_modifier
        
        return min(1.0, max(0.0, final_confidence))
    
    def _determine_trade_direction(self, signals: List[Signal], regime: MarketRegime) -> TradeDirection:
        """Determine overall trade direction from signals"""
        
        buy_score = 0
        sell_score = 0
        
        # Get regime weights
        weights = get_signal_weights(regime.value)
        
        for signal in signals:
            weight = weights.get(signal.signal_type.value, 0.25)  # Default weight
            confidence_weighted = signal.confidence * weight
            
            if signal.direction == TradeDirection.BUY:
                buy_score += confidence_weighted
            else:
                sell_score += confidence_weighted
        
        return TradeDirection.BUY if buy_score > sell_score else TradeDirection.SELL
    
    def _generate_reasoning(self, signals: List[Signal], regime: MarketRegime, sentiment: float, confidence: float) -> str:
        """Generate human-readable reasoning for the trade decision"""
        
        signal_summary = defaultdict(int)
        for signal in signals:
            signal_summary[signal.signal_type.value] += 1
        
        signal_desc = ", ".join([f"{count} {stype}" for stype, count in signal_summary.items()])
        
        sentiment_desc = "positive" if sentiment > 0.1 else "negative" if sentiment < -0.1 else "neutral"
        
        reasoning = (
            f"Trade decision based on {len(signals)} signals ({signal_desc}) "
            f"in {regime.value} market regime with {sentiment_desc} sentiment. "
            f"Combined confidence: {confidence:.2f}"
        )
        
        return reasoning
    
    def _should_make_decision(self, asset: str) -> bool:
        """Check if we should make a trading decision for this asset"""
        
        # Check cooldown period
        if asset in self.recent_decisions:
            time_since_decision = datetime.utcnow() - self.recent_decisions[asset]
            if time_since_decision < timedelta(minutes=self.decision_cooldown_minutes):
                return False
        
        # Check if we have enough signals
        recent_signals = self.signal_aggregator.get_recent_signals(asset, minutes_back=15)
        return len(recent_signals) >= 2  # Need at least 2 signals
    
    async def _process_proposed_trade(self, proposed_trade: ProposedTrade):
        """Process proposed trade through risk management and execution"""
        
        try:
            # Notify callbacks
            for callback in self.trade_callbacks:
                try:
                    await callback(proposed_trade)
                except Exception as e:
                    self.logger.error(f"Trade callback error: {e}")
            
            # Process through risk manager if available
            if self.risk_manager:
                sized_order = await self.risk_manager.validate_and_size(proposed_trade)
                
                if sized_order and self.execution_handler:
                    # Execute the trade
                    await self.execution_handler.place_order(sized_order)
                    
        except Exception as e:
            self.logger.error(f"Error processing proposed trade: {e}")
    
    def get_decision_stats(self) -> Dict:
        """Get decision engine statistics"""
        total_signals = sum(len(signals) for signals in self.signal_aggregator.signals.values())
        
        return {
            "total_signals_stored": total_signals,
            "assets_tracked": len(self.signal_aggregator.signals),
            "recent_decisions": len(self.recent_decisions),
            "current_regime": self.regime_classifier.current_regime.value,
            "regime_confidence": self.regime_classifier.regime_confidence,
            "entry_threshold": self.entry_threshold
        }
