"""
Telegram bot for trading alerts and system notifications.
"""

import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime

try:
    from telegram import Bo<PERSON>
    from telegram.error import TelegramError
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False

from ..shared.data_models import (
    ExecutedTrade, ProposedTrade, Signal, MarketRegime, SystemHealth
)
from ..shared.config import config


class TelegramNotifier:
    """Telegram bot for sending trading notifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.bot: Optional[Bot] = None
        self.chat_id = config.telegram_chat_id
        self.enabled = False
        
        if TELEGRAM_AVAILABLE and config.telegram_bot_token and self.chat_id:
            try:
                self.bot = Bot(token=config.telegram_bot_token)
                self.enabled = True
                self.logger.info("Telegram bot initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Telegram bot: {e}")
        else:
            self.logger.warning("Telegram bot not available - missing library or configuration")
    
    async def send_message(self, message: str, parse_mode: str = 'Markdown') -> bool:
        """Send a message to Telegram"""
        if not self.enabled:
            self.logger.debug(f"Telegram disabled - would send: {message}")
            return False
        
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=parse_mode
            )
            return True
            
        except TelegramError as e:
            self.logger.error(f"Telegram send failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending Telegram message: {e}")
            return False
    
    async def send_trade_alert(self, executed_trade: ExecutedTrade):
        """Send trade execution alert"""
        
        direction_emoji = "🟢" if executed_trade.direction.value == "BUY" else "🔴"
        status_emoji = "✅" if executed_trade.status.value == "FILLED" else "⏳"
        
        message = f"""
{direction_emoji} *TRADE EXECUTED* {status_emoji}

*Asset:* {executed_trade.asset}
*Direction:* {executed_trade.direction.value}
*Quantity:* {executed_trade.quantity:.6f}
*Entry Price:* ${executed_trade.entry_price:,.2f}
*Status:* {executed_trade.status.value}
*Time:* {executed_trade.timestamp.strftime('%H:%M:%S')}
"""
        
        if executed_trade.pnl is not None:
            pnl_emoji = "💰" if executed_trade.pnl > 0 else "💸"
            message += f"\n{pnl_emoji} *P&L:* ${executed_trade.pnl:+,.2f}"
        
        await self.send_message(message)
    
    async def send_signal_alert(self, signal: Signal):
        """Send high-confidence signal alert"""
        
        if signal.confidence < 0.8:  # Only send high-confidence signals
            return
        
        signal_emoji = {
            "chart": "📈",
            "whale": "🐋", 
            "sentiment": "💭",
            "spoof": "⚠️",
            "volume": "📊",
            "momentum": "🚀"
        }
        
        direction_emoji = "🟢" if signal.direction.value == "BUY" else "🔴"
        emoji = signal_emoji.get(signal.signal_type.value, "🔔")
        
        message = f"""
{emoji} *HIGH CONFIDENCE SIGNAL* {direction_emoji}

*Asset:* {signal.asset}
*Type:* {signal.signal_type.value.upper()}
*Direction:* {signal.direction.value}
*Confidence:* {signal.confidence:.1%}
*Source:* {signal.source}
*Time:* {signal.timestamp.strftime('%H:%M:%S')}
"""
        
        await self.send_message(message)
    
    async def send_proposed_trade_alert(self, proposed_trade: ProposedTrade):
        """Send proposed trade alert"""
        
        direction_emoji = "🟢" if proposed_trade.direction.value == "BUY" else "🔴"
        
        message = f"""
🎯 *TRADE PROPOSAL* {direction_emoji}

*Asset:* {proposed_trade.asset}
*Direction:* {proposed_trade.direction.value}
*Confidence:* {proposed_trade.confidence:.1%}
*Market Regime:* {proposed_trade.market_regime.value}
*Signals:* {len(proposed_trade.signals)}

*Reasoning:* {proposed_trade.reasoning[:200]}...
*Time:* {proposed_trade.timestamp.strftime('%H:%M:%S')}
"""
        
        await self.send_message(message)
    
    async def send_regime_change_alert(self, old_regime: MarketRegime, new_regime: MarketRegime, confidence: float):
        """Send market regime change alert"""
        
        regime_emoji = {
            "RANGE": "↔️",
            "BREAKOUT": "💥",
            "TREND_UP": "📈",
            "TREND_DOWN": "📉", 
            "HIGH_VOLATILITY": "⚡"
        }
        
        old_emoji = regime_emoji.get(old_regime.value, "❓")
        new_emoji = regime_emoji.get(new_regime.value, "❓")
        
        message = f"""
🔄 *MARKET REGIME CHANGE*

{old_emoji} *From:* {old_regime.value}
{new_emoji} *To:* {new_regime.value}
*Confidence:* {confidence:.1%}
*Time:* {datetime.now().strftime('%H:%M:%S')}

Strategy adjustments may be applied.
"""
        
        await self.send_message(message)
    
    async def send_system_alert(self, alert_type: str, message: str, severity: str = "INFO"):
        """Send system alert"""
        
        severity_emoji = {
            "INFO": "ℹ️",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "CRITICAL": "🚨"
        }
        
        emoji = severity_emoji.get(severity, "ℹ️")
        
        alert_message = f"""
{emoji} *SYSTEM ALERT*

*Type:* {alert_type}
*Severity:* {severity}
*Message:* {message}
*Time:* {datetime.now().strftime('%H:%M:%S')}
"""
        
        await self.send_message(alert_message)
    
    async def send_daily_summary(self, portfolio_summary: Dict):
        """Send daily portfolio summary"""
        
        pnl_emoji = "💰" if portfolio_summary.get('daily_pnl', 0) > 0 else "💸"
        
        message = f"""
📊 *DAILY SUMMARY*

{pnl_emoji} *Daily P&L:* ${portfolio_summary.get('daily_pnl', 0):+,.2f}
💼 *Portfolio Value:* ${portfolio_summary.get('total_value', 0):,.2f}
📈 *Total P&L:* ${portfolio_summary.get('total_pnl', 0):+,.2f}
🎯 *Open Positions:* {portfolio_summary.get('open_positions', 0)}
🏆 *Win Rate:* {portfolio_summary.get('win_rate', 0):.1%}
📅 *Date:* {datetime.now().strftime('%Y-%m-%d')}
"""
        
        await self.send_message(message)
    
    async def send_startup_message(self):
        """Send system startup notification"""
        message = f"""
🚀 *SYSCRYP TRADING SYSTEM STARTED*

*Status:* Online
*Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
*Mode:* {'Paper Trading' if config.max_position_size_usd < 1000 else 'Live Trading'}

System is ready for trading signals.
"""
        
        await self.send_message(message)
    
    async def send_shutdown_message(self):
        """Send system shutdown notification"""
        message = f"""
🛑 *SYSCRYP TRADING SYSTEM SHUTDOWN*

*Status:* Offline
*Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

All trading activities have been stopped.
"""
        
        await self.send_message(message)


class AlertManager:
    """Manages different types of alerts and notifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.telegram = TelegramNotifier()
        
        # Alert settings
        self.signal_confidence_threshold = 0.8
        self.trade_alert_enabled = True
        self.signal_alert_enabled = True
        self.regime_alert_enabled = True
        self.system_alert_enabled = True
        
        # Rate limiting
        self.last_signal_alert = {}  # asset -> timestamp
        self.signal_alert_cooldown = 300  # 5 minutes
        
    async def handle_trade_executed(self, executed_trade: ExecutedTrade):
        """Handle trade execution event"""
        if self.trade_alert_enabled:
            await self.telegram.send_trade_alert(executed_trade)
    
    async def handle_signal_generated(self, signal: Signal):
        """Handle new signal event"""
        if not self.signal_alert_enabled:
            return
        
        # Check confidence threshold
        if signal.confidence < self.signal_confidence_threshold:
            return
        
        # Check rate limiting
        now = datetime.now()
        last_alert = self.last_signal_alert.get(signal.asset)
        
        if last_alert and (now - last_alert).total_seconds() < self.signal_alert_cooldown:
            return
        
        await self.telegram.send_signal_alert(signal)
        self.last_signal_alert[signal.asset] = now
    
    async def handle_proposed_trade(self, proposed_trade: ProposedTrade):
        """Handle proposed trade event"""
        if self.trade_alert_enabled:
            await self.telegram.send_proposed_trade_alert(proposed_trade)
    
    async def handle_regime_change(self, old_regime: MarketRegime, new_regime: MarketRegime, confidence: float):
        """Handle market regime change"""
        if self.regime_alert_enabled:
            await self.telegram.send_regime_change_alert(old_regime, new_regime, confidence)
    
    async def handle_system_event(self, event_type: str, message: str, severity: str = "INFO"):
        """Handle system events"""
        if self.system_alert_enabled:
            await self.telegram.send_system_alert(event_type, message, severity)
    
    async def send_daily_summary(self, portfolio_summary: Dict):
        """Send daily summary"""
        await self.telegram.send_daily_summary(portfolio_summary)
    
    async def system_startup(self):
        """Handle system startup"""
        await self.telegram.send_startup_message()
    
    async def system_shutdown(self):
        """Handle system shutdown"""
        await self.telegram.send_shutdown_message()
    
    def configure_alerts(self, 
                        trade_alerts: bool = True,
                        signal_alerts: bool = True, 
                        regime_alerts: bool = True,
                        system_alerts: bool = True,
                        signal_threshold: float = 0.8):
        """Configure alert settings"""
        self.trade_alert_enabled = trade_alerts
        self.signal_alert_enabled = signal_alerts
        self.regime_alert_enabled = regime_alerts
        self.system_alert_enabled = system_alerts
        self.signal_confidence_threshold = signal_threshold
        
        self.logger.info(f"Alert settings updated: trades={trade_alerts}, signals={signal_alerts}, regime={regime_alerts}, system={system_alerts}")


# Global alert manager instance
alert_manager = AlertManager()
