"""
Exchange execution handler for placing and managing trades.
Handles order placement, tracking, and portfolio management.
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Optional, Callable
from datetime import datetime
import hashlib
import hmac
import time

import aiohttp

from ..shared.data_models import (
    SizedTradeOrder, ExecutedTrade, TradeStatus, TradeDirection
)
from ..shared.config import config


class BinanceExecutionClient:
    """Binance API client for trade execution"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.api_key = config.binance_api_key
        self.secret_key = config.binance_secret_key
        self.base_url = "https://api.binance.com"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature for Binance API"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def _make_request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """Make authenticated request to Binance API"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        url = f"{self.base_url}{endpoint}"
        headers = {"X-MBX-APIKEY": self.api_key}
        
        if params is None:
            params = {}
            
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            params['signature'] = self._generate_signature(query_string)
        
        try:
            if method.upper() == 'GET':
                async with self.session.get(url, params=params, headers=headers) as response:
                    return await response.json()
            elif method.upper() == 'POST':
                async with self.session.post(url, data=params, headers=headers) as response:
                    return await response.json()
            elif method.upper() == 'DELETE':
                async with self.session.delete(url, params=params, headers=headers) as response:
                    return await response.json()
                    
        except Exception as e:
            self.logger.error(f"Binance API request failed: {e}")
            return {}
    
    async def place_market_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """Place a market order"""
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'MARKET',
            'quantity': f"{quantity:.8f}".rstrip('0').rstrip('.')
        }
        
        return await self._make_request('POST', '/api/v3/order', params, signed=True)
    
    async def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict:
        """Place a limit order"""
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'LIMIT',
            'timeInForce': 'GTC',
            'quantity': f"{quantity:.8f}".rstrip('0').rstrip('.'),
            'price': f"{price:.8f}".rstrip('0').rstrip('.')
        }
        
        return await self._make_request('POST', '/api/v3/order', params, signed=True)
    
    async def place_stop_loss_order(self, symbol: str, side: str, quantity: float, stop_price: float) -> Dict:
        """Place a stop loss order"""
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'STOP_MARKET',
            'quantity': f"{quantity:.8f}".rstrip('0').rstrip('.'),
            'stopPrice': f"{stop_price:.8f}".rstrip('0').rstrip('.')
        }
        
        return await self._make_request('POST', '/api/v3/order', params, signed=True)
    
    async def get_order_status(self, symbol: str, order_id: int) -> Dict:
        """Get order status"""
        params = {
            'symbol': symbol,
            'orderId': order_id
        }
        
        return await self._make_request('GET', '/api/v3/order', params, signed=True)
    
    async def cancel_order(self, symbol: str, order_id: int) -> Dict:
        """Cancel an order"""
        params = {
            'symbol': symbol,
            'orderId': order_id
        }
        
        return await self._make_request('DELETE', '/api/v3/order', params, signed=True)
    
    async def get_account_info(self) -> Dict:
        """Get account information"""
        return await self._make_request('GET', '/api/v3/account', signed=True)


class MockExecutionClient:
    """Mock execution client for testing without real trades"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.orders: Dict[int, Dict] = {}
        self.next_order_id = 1000
        
    async def place_market_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """Simulate market order placement"""
        order_id = self.next_order_id
        self.next_order_id += 1
        
        # Simulate successful order
        order = {
            'orderId': order_id,
            'symbol': symbol,
            'status': 'FILLED',
            'type': 'MARKET',
            'side': side.upper(),
            'executedQty': str(quantity),
            'fills': [{
                'price': '45000.0' if 'BTC' in symbol else '3000.0',  # Mock prices
                'qty': str(quantity),
                'commission': '0.001',
                'commissionAsset': 'BNB'
            }]
        }
        
        self.orders[order_id] = order
        self.logger.info(f"Mock order placed: {order_id} - {side} {quantity} {symbol}")
        
        return order
    
    async def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict:
        """Simulate limit order placement"""
        order_id = self.next_order_id
        self.next_order_id += 1
        
        order = {
            'orderId': order_id,
            'symbol': symbol,
            'status': 'NEW',
            'type': 'LIMIT',
            'side': side.upper(),
            'origQty': str(quantity),
            'price': str(price)
        }
        
        self.orders[order_id] = order
        return order
    
    async def get_order_status(self, symbol: str, order_id: int) -> Dict:
        """Get mock order status"""
        return self.orders.get(order_id, {})
    
    async def cancel_order(self, symbol: str, order_id: int) -> Dict:
        """Cancel mock order"""
        if order_id in self.orders:
            self.orders[order_id]['status'] = 'CANCELED'
            return self.orders[order_id]
        return {}


class ExecutionHandler:
    """Main execution handler for managing trades"""
    
    def __init__(self, use_mock: bool = True):
        self.logger = logging.getLogger(__name__)
        self.use_mock = use_mock
        
        # Initialize execution client
        if use_mock:
            self.client = MockExecutionClient()
        else:
            self.client = BinanceExecutionClient()
        
        # Track active orders and trades
        self.active_orders: Dict[str, Dict] = {}  # trade_id -> order_info
        self.executed_trades: Dict[str, ExecutedTrade] = {}
        
        # Callbacks for trade events
        self.trade_callbacks: List[Callable] = []
        
    def add_trade_callback(self, callback: Callable):
        """Add callback for trade events"""
        self.trade_callbacks.append(callback)
        
    async def place_order(self, sized_order: SizedTradeOrder) -> Optional[ExecutedTrade]:
        """Place an order and return executed trade"""
        
        try:
            trade_id = str(uuid.uuid4())
            
            # Convert direction to Binance format
            side = 'BUY' if sized_order.direction == TradeDirection.BUY else 'SELL'
            
            # Place main order
            if sized_order.order_type == 'MARKET':
                order_response = await self.client.place_market_order(
                    sized_order.asset,
                    side,
                    sized_order.quantity
                )
            else:
                order_response = await self.client.place_limit_order(
                    sized_order.asset,
                    side,
                    sized_order.quantity,
                    sized_order.price
                )
            
            if not order_response or 'orderId' not in order_response:
                self.logger.error(f"Order placement failed for {sized_order.asset}")
                return None
            
            # Extract execution details
            executed_price = self._extract_execution_price(order_response)
            executed_quantity = float(order_response.get('executedQty', sized_order.quantity))
            
            # Create executed trade
            executed_trade = ExecutedTrade(
                trade_id=trade_id,
                asset=sized_order.asset,
                direction=sized_order.direction,
                quantity=executed_quantity,
                entry_price=executed_price,
                status=TradeStatus.FILLED if order_response.get('status') == 'FILLED' else TradeStatus.PENDING
            )
            
            # Store trade
            self.executed_trades[trade_id] = executed_trade
            self.active_orders[trade_id] = {
                'main_order_id': order_response['orderId'],
                'sized_order': sized_order,
                'stop_loss_order_id': None,
                'take_profit_order_id': None
            }
            
            # Place stop loss and take profit orders if specified
            if executed_trade.status == TradeStatus.FILLED:
                await self._place_exit_orders(trade_id, sized_order, executed_quantity)
            
            # Notify callbacks
            await self._notify_trade_callbacks(executed_trade)
            
            self.logger.info(f"Trade executed: {trade_id} - {side} {executed_quantity} {sized_order.asset} at ${executed_price}")
            
            return executed_trade
            
        except Exception as e:
            self.logger.error(f"Order execution failed: {e}")
            return None
    
    async def _place_exit_orders(self, trade_id: str, sized_order: SizedTradeOrder, quantity: float):
        """Place stop loss and take profit orders"""
        
        try:
            # Determine exit order sides (opposite of entry)
            exit_side = 'SELL' if sized_order.direction == TradeDirection.BUY else 'BUY'
            
            # Place stop loss order
            if sized_order.stop_loss:
                stop_order = await self.client.place_stop_loss_order(
                    sized_order.asset,
                    exit_side,
                    quantity,
                    sized_order.stop_loss
                )
                
                if stop_order and 'orderId' in stop_order:
                    self.active_orders[trade_id]['stop_loss_order_id'] = stop_order['orderId']
                    self.logger.info(f"Stop loss placed for {trade_id} at ${sized_order.stop_loss}")
            
            # Place take profit order (as limit order)
            if sized_order.take_profit:
                tp_order = await self.client.place_limit_order(
                    sized_order.asset,
                    exit_side,
                    quantity,
                    sized_order.take_profit
                )
                
                if tp_order and 'orderId' in tp_order:
                    self.active_orders[trade_id]['take_profit_order_id'] = tp_order['orderId']
                    self.logger.info(f"Take profit placed for {trade_id} at ${sized_order.take_profit}")
                    
        except Exception as e:
            self.logger.error(f"Failed to place exit orders for {trade_id}: {e}")
    
    def _extract_execution_price(self, order_response: Dict) -> float:
        """Extract execution price from order response"""
        
        # For market orders, get price from fills
        if 'fills' in order_response and order_response['fills']:
            total_value = 0
            total_quantity = 0
            
            for fill in order_response['fills']:
                price = float(fill['price'])
                qty = float(fill['qty'])
                total_value += price * qty
                total_quantity += qty
            
            return total_value / total_quantity if total_quantity > 0 else 0
        
        # For limit orders, use the order price
        elif 'price' in order_response:
            return float(order_response['price'])
        
        # Fallback
        return 0.0
    
    async def _notify_trade_callbacks(self, executed_trade: ExecutedTrade):
        """Notify all trade callbacks"""
        for callback in self.trade_callbacks:
            try:
                await callback(executed_trade)
            except Exception as e:
                self.logger.error(f"Trade callback error: {e}")
    
    async def monitor_orders(self):
        """Monitor active orders for fills and updates"""
        while True:
            try:
                for trade_id, order_info in list(self.active_orders.items()):
                    await self._check_order_status(trade_id, order_info)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Order monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _check_order_status(self, trade_id: str, order_info: Dict):
        """Check status of orders for a trade"""
        
        try:
            executed_trade = self.executed_trades.get(trade_id)
            if not executed_trade:
                return
            
            sized_order = order_info['sized_order']
            
            # Check stop loss order
            if order_info.get('stop_loss_order_id'):
                sl_status = await self.client.get_order_status(
                    sized_order.asset, 
                    order_info['stop_loss_order_id']
                )
                
                if sl_status.get('status') == 'FILLED':
                    await self._handle_exit_fill(trade_id, sl_status, 'stop_loss')
            
            # Check take profit order
            if order_info.get('take_profit_order_id'):
                tp_status = await self.client.get_order_status(
                    sized_order.asset,
                    order_info['take_profit_order_id']
                )
                
                if tp_status.get('status') == 'FILLED':
                    await self._handle_exit_fill(trade_id, tp_status, 'take_profit')
                    
        except Exception as e:
            self.logger.error(f"Order status check failed for {trade_id}: {e}")
    
    async def _handle_exit_fill(self, trade_id: str, order_response: Dict, exit_type: str):
        """Handle exit order fill"""
        
        executed_trade = self.executed_trades.get(trade_id)
        if not executed_trade:
            return
        
        # Update trade with exit information
        exit_price = self._extract_execution_price(order_response)
        executed_trade.exit_price = exit_price
        executed_trade.exit_timestamp = datetime.utcnow()
        
        # Calculate P&L
        if executed_trade.direction == TradeDirection.BUY:
            pnl = (exit_price - executed_trade.entry_price) * executed_trade.quantity
        else:
            pnl = (executed_trade.entry_price - exit_price) * executed_trade.quantity
        
        executed_trade.pnl = pnl
        executed_trade.status = TradeStatus.FILLED
        
        # Remove from active orders
        if trade_id in self.active_orders:
            del self.active_orders[trade_id]
        
        # Notify callbacks
        await self._notify_trade_callbacks(executed_trade)
        
        self.logger.info(f"Trade {trade_id} closed via {exit_type}: P&L ${pnl:.2f}")
    
    def get_execution_stats(self) -> Dict:
        """Get execution statistics"""
        total_trades = len(self.executed_trades)
        active_trades = len(self.active_orders)
        
        filled_trades = [t for t in self.executed_trades.values() if t.status == TradeStatus.FILLED and t.pnl is not None]
        total_pnl = sum(t.pnl for t in filled_trades)
        
        return {
            "total_trades": total_trades,
            "active_trades": active_trades,
            "completed_trades": len(filled_trades),
            "total_pnl": total_pnl,
            "using_mock": self.use_mock
        }
