"""
Configuration management for the trading system.
"""

import os
from typing import Dict, Any
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class TradingConfig(BaseSettings):
    """Trading system configuration"""
    
    # API Keys
    binance_api_key: str = Field(..., env="BINANCE_API_KEY")
    binance_secret_key: str = Field(..., env="BINANCE_SECRET_KEY")
    etherscan_api_key: str = Field(..., env="ETHERSCAN_API_KEY")
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    
    # Social Media APIs
    twitter_bearer_token: str = Field("", env="TWITTER_BEARER_TOKEN")
    telegram_bot_token: str = Field("", env="TELEGRAM_BOT_TOKEN")
    telegram_chat_id: str = Field("", env="TELEGRAM_CHAT_ID")
    
    # Trading Parameters
    max_position_size_usd: float = Field(1000.0, env="MAX_POSITION_SIZE_USD")
    risk_per_trade_percent: float = Field(1.0, env="RISK_PER_TRADE_PERCENT")
    max_open_positions: int = Field(5, env="MAX_OPEN_POSITIONS")
    entry_confidence_threshold: float = Field(0.7, env="ENTRY_CONFIDENCE_THRESHOLD")
    
    # System Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    database_url: str = Field("sqlite:///./trading_system.db", env="DATABASE_URL")
    
    # Market Data URLs
    binance_ws_url: str = Field("wss://stream.binance.com:9443/ws/", env="BINANCE_WS_URL")
    etherscan_base_url: str = Field("https://api.etherscan.io/api", env="ETHERSCAN_BASE_URL")
    
    # AI Configuration
    gemini_model: str = Field("gemini-pro", env="GEMINI_MODEL")
    max_contract_analysis_tokens: int = Field(8192, env="MAX_CONTRACT_ANALYSIS_TOKENS")
    
    # Signal Weights for Different Market Regimes
    regime_weights: Dict[str, Dict[str, float]] = {
        "RANGE": {
            "chart": 0.4,
            "whale": 0.3,
            "sentiment": 0.2,
            "spoof": 0.1
        },
        "BREAKOUT": {
            "chart": 0.5,
            "whale": 0.3,
            "sentiment": 0.1,
            "spoof": 0.1
        },
        "TREND_UP": {
            "chart": 0.3,
            "whale": 0.4,
            "sentiment": 0.2,
            "spoof": 0.1
        },
        "TREND_DOWN": {
            "chart": 0.3,
            "whale": 0.4,
            "sentiment": 0.2,
            "spoof": 0.1
        },
        "HIGH_VOLATILITY": {
            "chart": 0.2,
            "whale": 0.5,
            "sentiment": 0.1,
            "spoof": 0.2
        }
    }
    
    # Technical Indicator Parameters
    rsi_period: int = 14
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0
    bollinger_period: int = 20
    bollinger_std: float = 2.0
    
    # Whale Detection Thresholds
    whale_threshold_btc: float = 100.0  # BTC
    whale_threshold_eth: float = 1000.0  # ETH
    whale_threshold_usd: float = 1000000.0  # USD value
    
    # Spoofing Detection Parameters
    spoof_size_multiplier: float = 10.0  # Order must be 10x average
    spoof_time_threshold: float = 5.0  # Seconds on book
    spoof_volume_threshold: float = 0.1  # Max volume traded at level
    
    # Risk Management
    max_portfolio_risk: float = 0.05  # 5% of portfolio at risk
    correlation_threshold: float = 0.7  # Max correlation between positions
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global configuration instance
config = TradingConfig()


def get_signal_weights(regime: str) -> Dict[str, float]:
    """Get signal weights for a specific market regime"""
    return config.regime_weights.get(regime, config.regime_weights["RANGE"])


def validate_config() -> bool:
    """Validate that all required configuration is present"""
    required_fields = [
        "binance_api_key",
        "binance_secret_key", 
        "etherscan_api_key",
        "gemini_api_key"
    ]
    
    for field in required_fields:
        if not getattr(config, field):
            print(f"Missing required configuration: {field}")
            return False
    
    return True
