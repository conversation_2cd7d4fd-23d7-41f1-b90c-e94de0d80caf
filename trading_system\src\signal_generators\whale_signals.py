"""
Whale activity signal generation.
Analyzes large wallet movements and generates trading signals.
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from ..shared.data_models import Signal, SignalType, TradeDirection, WhaleActivity
from ..shared.config import config


class WhaleSignalGenerator:
    """Generates trading signals from whale activity"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.whale_threshold_btc = config.whale_threshold_btc
        self.whale_threshold_eth = config.whale_threshold_eth
        self.whale_threshold_usd = config.whale_threshold_usd
        
        # Exchange addresses for flow analysis
        self.exchange_addresses = {
            'binance': [
                '******************************************',
                '******************************************',
                '******************************************',
                '******************************************'
            ],
            'coinbase': [
                '******************************************',
                '******************************************',
                '******************************************'
            ],
            'kraken': [
                '******************************************',
                '******************************************'
            ]
        }
        
    def analyze_whale_activity(self, whale_activities: List[WhaleActivity]) -> List[Signal]:
        """Analyze whale activities and generate trading signals"""
        signals = []
        
        if not whale_activities:
            return signals
            
        # Group activities by asset and time window
        asset_activities = self._group_activities_by_asset(whale_activities)
        
        for asset, activities in asset_activities.items():
            asset_signals = self._analyze_asset_whale_activity(asset, activities)
            signals.extend(asset_signals)
            
        return signals
    
    def _group_activities_by_asset(self, activities: List[WhaleActivity]) -> Dict[str, List[WhaleActivity]]:
        """Group whale activities by asset"""
        grouped = {}
        
        for activity in activities:
            asset = activity.asset.upper()
            if asset not in grouped:
                grouped[asset] = []
            grouped[asset].append(activity)
            
        return grouped
    
    def _analyze_asset_whale_activity(self, asset: str, activities: List[WhaleActivity]) -> List[Signal]:
        """Analyze whale activities for a specific asset"""
        signals = []
        
        # Analyze exchange flows
        exchange_flow_signals = self._analyze_exchange_flows(asset, activities)
        signals.extend(exchange_flow_signals)
        
        # Analyze accumulation patterns
        accumulation_signals = self._analyze_accumulation_patterns(asset, activities)
        signals.extend(accumulation_signals)
        
        # Analyze large single transactions
        large_tx_signals = self._analyze_large_transactions(asset, activities)
        signals.extend(large_tx_signals)
        
        return signals
    
    def _analyze_exchange_flows(self, asset: str, activities: List[WhaleActivity]) -> List[Signal]:
        """Analyze flows to/from exchanges"""
        signals = []
        
        # Separate inflows and outflows
        exchange_inflows = []
        exchange_outflows = []
        
        for activity in activities:
            if activity.exchange_flow:
                if self._is_exchange_address(activity.to_address):
                    exchange_inflows.append(activity)
                elif self._is_exchange_address(activity.from_address):
                    exchange_outflows.append(activity)
        
        # Analyze inflows (potentially bearish - selling pressure)
        if exchange_inflows:
            total_inflow = sum(activity.amount for activity in exchange_inflows)
            
            if self._is_significant_amount(asset, total_inflow):
                confidence = self._calculate_flow_confidence(asset, total_inflow, len(exchange_inflows))
                
                signals.append(Signal(
                    asset=asset,
                    signal_type=SignalType.WHALE,
                    confidence=confidence,
                    direction=TradeDirection.SELL,
                    source="Exchange_Inflow",
                    details={
                        "total_inflow": total_inflow,
                        "transaction_count": len(exchange_inflows),
                        "exchanges": list(set(activity.whale_label for activity in exchange_inflows if activity.whale_label)),
                        "time_window": "1h"
                    }
                ))
        
        # Analyze outflows (potentially bullish - accumulation)
        if exchange_outflows:
            total_outflow = sum(activity.amount for activity in exchange_outflows)
            
            if self._is_significant_amount(asset, total_outflow):
                confidence = self._calculate_flow_confidence(asset, total_outflow, len(exchange_outflows))
                
                signals.append(Signal(
                    asset=asset,
                    signal_type=SignalType.WHALE,
                    confidence=confidence,
                    direction=TradeDirection.BUY,
                    source="Exchange_Outflow",
                    details={
                        "total_outflow": total_outflow,
                        "transaction_count": len(exchange_outflows),
                        "exchanges": list(set(activity.whale_label for activity in exchange_outflows if activity.whale_label)),
                        "time_window": "1h"
                    }
                ))
        
        return signals
    
    def _analyze_accumulation_patterns(self, asset: str, activities: List[WhaleActivity]) -> List[Signal]:
        """Analyze whale accumulation patterns"""
        signals = []
        
        # Group by wallet address
        wallet_activities = {}
        for activity in activities:
            if not activity.exchange_flow:  # Focus on non-exchange wallets
                wallet = activity.to_address
                if wallet not in wallet_activities:
                    wallet_activities[wallet] = []
                wallet_activities[wallet].append(activity)
        
        # Look for accumulation patterns
        for wallet, wallet_acts in wallet_activities.items():
            if len(wallet_acts) >= 3:  # Multiple transactions to same wallet
                total_accumulated = sum(act.amount for act in wallet_acts)
                
                if self._is_significant_amount(asset, total_accumulated):
                    # This suggests accumulation
                    confidence = min(0.8, len(wallet_acts) * 0.1 + 0.4)
                    
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.WHALE,
                        confidence=confidence,
                        direction=TradeDirection.BUY,
                        source="Whale_Accumulation",
                        details={
                            "wallet": wallet,
                            "total_accumulated": total_accumulated,
                            "transaction_count": len(wallet_acts),
                            "time_span": (max(act.timestamp for act in wallet_acts) - 
                                        min(act.timestamp for act in wallet_acts)).total_seconds() / 3600
                        }
                    ))
        
        return signals
    
    def _analyze_large_transactions(self, asset: str, activities: List[WhaleActivity]) -> List[Signal]:
        """Analyze individual large transactions"""
        signals = []
        
        for activity in activities:
            if self._is_very_large_transaction(asset, activity.amount):
                # Very large single transaction
                confidence = self._calculate_large_tx_confidence(asset, activity.amount)
                
                # Direction depends on context
                if activity.exchange_flow:
                    if self._is_exchange_address(activity.to_address):
                        direction = TradeDirection.SELL  # Moving to exchange
                    else:
                        direction = TradeDirection.BUY   # Moving from exchange
                else:
                    # For non-exchange flows, assume accumulation is bullish
                    direction = TradeDirection.BUY
                
                signals.append(Signal(
                    asset=asset,
                    signal_type=SignalType.WHALE,
                    confidence=confidence,
                    direction=direction,
                    source="Large_Transaction",
                    details={
                        "amount": activity.amount,
                        "from_address": activity.from_address,
                        "to_address": activity.to_address,
                        "transaction_hash": activity.transaction_hash,
                        "whale_label": activity.whale_label,
                        "exchange_flow": activity.exchange_flow
                    }
                ))
        
        return signals
    
    def _is_exchange_address(self, address: str) -> bool:
        """Check if address belongs to a known exchange"""
        address = address.lower()
        for exchange, addresses in self.exchange_addresses.items():
            if address in [addr.lower() for addr in addresses]:
                return True
        return False
    
    def _is_significant_amount(self, asset: str, amount: float) -> bool:
        """Check if amount is significant for the asset"""
        if asset == 'BTC':
            return amount >= self.whale_threshold_btc
        elif asset == 'ETH':
            return amount >= self.whale_threshold_eth
        else:
            # For other assets, use USD threshold (would need price conversion)
            return amount >= 100  # Placeholder
    
    def _is_very_large_transaction(self, asset: str, amount: float) -> bool:
        """Check if this is a very large single transaction"""
        if asset == 'BTC':
            return amount >= self.whale_threshold_btc * 5
        elif asset == 'ETH':
            return amount >= self.whale_threshold_eth * 5
        else:
            return amount >= 500  # Placeholder
    
    def _calculate_flow_confidence(self, asset: str, amount: float, tx_count: int) -> float:
        """Calculate confidence score for exchange flows"""
        base_confidence = 0.5
        
        # Amount factor
        if asset == 'BTC':
            amount_factor = min(0.3, amount / (self.whale_threshold_btc * 10))
        elif asset == 'ETH':
            amount_factor = min(0.3, amount / (self.whale_threshold_eth * 10))
        else:
            amount_factor = 0.2
        
        # Transaction count factor
        tx_factor = min(0.2, tx_count * 0.05)
        
        return min(0.9, base_confidence + amount_factor + tx_factor)
    
    def _calculate_large_tx_confidence(self, asset: str, amount: float) -> float:
        """Calculate confidence score for large transactions"""
        base_confidence = 0.6
        
        if asset == 'BTC':
            amount_factor = min(0.3, amount / (self.whale_threshold_btc * 20))
        elif asset == 'ETH':
            amount_factor = min(0.3, amount / (self.whale_threshold_eth * 20))
        else:
            amount_factor = 0.2
        
        return min(0.9, base_confidence + amount_factor)


class WhaleActivityAggregator:
    """Aggregates and processes whale activity data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.signal_generator = WhaleSignalGenerator()
        self.recent_activities: List[WhaleActivity] = []
        
    def add_whale_activity(self, activity: WhaleActivity):
        """Add new whale activity"""
        self.recent_activities.append(activity)
        
        # Keep only recent activities (last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.recent_activities = [
            act for act in self.recent_activities 
            if act.timestamp > cutoff_time
        ]
    
    def get_recent_signals(self, hours_back: int = 1) -> List[Signal]:
        """Get signals from recent whale activity"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
        recent_activities = [
            act for act in self.recent_activities 
            if act.timestamp > cutoff_time
        ]
        
        return self.signal_generator.analyze_whale_activity(recent_activities)
    
    def get_activity_summary(self, asset: str = None) -> Dict:
        """Get summary of recent whale activity"""
        activities = self.recent_activities
        
        if asset:
            activities = [act for act in activities if act.asset.upper() == asset.upper()]
        
        if not activities:
            return {}
        
        return {
            "total_transactions": len(activities),
            "total_volume": sum(act.amount for act in activities),
            "unique_addresses": len(set(act.from_address for act in activities) | 
                                 set(act.to_address for act in activities)),
            "exchange_flows": len([act for act in activities if act.exchange_flow]),
            "time_range": {
                "start": min(act.timestamp for act in activities),
                "end": max(act.timestamp for act in activities)
            }
        }
