"""
Social media scraping module for sentiment analysis.
Handles Twitter and Telegram data collection.
"""

import asyncio
import logging
import re
from typing import List, Dict, Optional, Callable
from datetime import datetime, timedelta
import aiohttp

try:
    import tweepy
    TWITTER_AVAILABLE = True
except ImportError:
    TWITTER_AVAILABLE = False
    
try:
    from telethon import TelegramClient
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False

from ..shared.config import config


class TwitterScraper:
    """Twitter data scraper for crypto sentiment"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.running = False
        
        if TWITTER_AVAILABLE and config.twitter_bearer_token:
            self.client = tweepy.Client(bearer_token=config.twitter_bearer_token)
        else:
            self.logger.warning("Twitter API not available - missing tweepy or bearer token")
            
    async def search_crypto_tweets(self, symbols: List[str], count: int = 100) -> List[Dict]:
        """Search for tweets mentioning crypto symbols"""
        if not self.client:
            return []
            
        tweets_data = []
        
        for symbol in symbols:
            try:
                # Create search query
                query = f"${symbol} OR #{symbol} OR {symbol} -is:retweet lang:en"
                
                # Search tweets
                tweets = tweepy.Paginator(
                    self.client.search_recent_tweets,
                    query=query,
                    max_results=min(count, 100),  # Twitter API limit
                    tweet_fields=['created_at', 'public_metrics', 'author_id']
                ).flatten(limit=count)
                
                for tweet in tweets:
                    tweet_data = {
                        'id': tweet.id,
                        'text': tweet.text,
                        'created_at': tweet.created_at,
                        'author_id': tweet.author_id,
                        'retweet_count': tweet.public_metrics['retweet_count'],
                        'like_count': tweet.public_metrics['like_count'],
                        'reply_count': tweet.public_metrics['reply_count'],
                        'symbol': symbol,
                        'source': 'twitter'
                    }
                    tweets_data.append(tweet_data)
                    
                # Rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error searching tweets for {symbol}: {e}")
                
        return tweets_data
        
    async def get_trending_crypto_topics(self) -> List[str]:
        """Get trending crypto-related topics"""
        if not self.client:
            return []
            
        try:
            # Get trending topics for worldwide
            trends = self.client.get_place_trends(id=1)  # 1 = worldwide
            
            crypto_trends = []
            crypto_keywords = ['bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'defi', 'nft']
            
            for trend in trends[0]['trends']:
                trend_name = trend['name'].lower()
                if any(keyword in trend_name for keyword in crypto_keywords):
                    crypto_trends.append(trend['name'])
                    
            return crypto_trends
            
        except Exception as e:
            self.logger.error(f"Error getting trending topics: {e}")
            return []


class TelegramScraper:
    """Telegram channel scraper for crypto signals"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.running = False
        
        # Popular crypto Telegram channels
        self.crypto_channels = [
            '@cryptonews',
            '@binance',
            '@coindesk',
            '@cointelegraph',
            '@whale_alert'
        ]
        
    async def initialize(self):
        """Initialize Telegram client"""
        if not TELEGRAM_AVAILABLE:
            self.logger.warning("Telegram scraping not available - missing telethon")
            return False
            
        try:
            # Note: This requires API credentials from Telegram
            # For production, you'd need to register an app at https://my.telegram.org
            self.client = TelegramClient('session', 'api_id', 'api_hash')
            await self.client.start()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Telegram client: {e}")
            return False
            
    async def scrape_channel_messages(self, channel: str, limit: int = 50) -> List[Dict]:
        """Scrape recent messages from a Telegram channel"""
        if not self.client:
            return []
            
        messages_data = []
        
        try:
            async for message in self.client.iter_messages(channel, limit=limit):
                if message.text:
                    message_data = {
                        'id': message.id,
                        'text': message.text,
                        'date': message.date,
                        'channel': channel,
                        'views': getattr(message, 'views', 0),
                        'forwards': getattr(message, 'forwards', 0),
                        'source': 'telegram'
                    }
                    messages_data.append(message_data)
                    
        except Exception as e:
            self.logger.error(f"Error scraping channel {channel}: {e}")
            
        return messages_data
        
    async def monitor_channels(self, callback: Callable):
        """Monitor multiple channels for new messages"""
        if not self.client:
            return
            
        self.running = True
        
        while self.running:
            try:
                for channel in self.crypto_channels:
                    messages = await self.scrape_channel_messages(channel, limit=10)
                    
                    for message in messages:
                        await callback(message)
                        
                    await asyncio.sleep(5)  # Rate limiting
                    
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error monitoring channels: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
                
    async def stop(self):
        """Stop monitoring"""
        self.running = False
        if self.client:
            await self.client.disconnect()


class SocialDataAggregator:
    """Aggregates social media data from multiple sources"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.twitter_scraper = TwitterScraper()
        self.telegram_scraper = TelegramScraper()
        self.callbacks: List[Callable] = []
        
    async def initialize(self):
        """Initialize all scrapers"""
        await self.telegram_scraper.initialize()
        
    def add_callback(self, callback: Callable):
        """Add callback for new social data"""
        self.callbacks.append(callback)
        
    async def collect_social_data(self, symbols: List[str]) -> Dict[str, List[Dict]]:
        """Collect social data for given symbols"""
        social_data = {
            'twitter': [],
            'telegram': []
        }
        
        # Collect Twitter data
        twitter_data = await self.twitter_scraper.search_crypto_tweets(symbols)
        social_data['twitter'] = twitter_data
        
        # Collect Telegram data
        for channel in self.telegram_scraper.crypto_channels:
            telegram_data = await self.telegram_scraper.scrape_channel_messages(channel)
            social_data['telegram'].extend(telegram_data)
            
        return social_data
        
    async def start_monitoring(self, symbols: List[str]):
        """Start continuous social media monitoring"""
        self.logger.info(f"Starting social media monitoring for: {symbols}")
        
        # Start Telegram monitoring
        async def telegram_callback(message):
            # Filter for relevant symbols
            text = message['text'].lower()
            for symbol in symbols:
                if symbol.lower() in text or f"${symbol.lower()}" in text:
                    for callback in self.callbacks:
                        await callback(message)
                    break
                    
        await self.telegram_scraper.monitor_channels(telegram_callback)
        
    async def stop(self):
        """Stop all monitoring"""
        await self.telegram_scraper.stop()
        self.logger.info("Social media monitoring stopped")


def extract_crypto_symbols(text: str) -> List[str]:
    """Extract cryptocurrency symbols from text"""
    # Pattern to match $SYMBOL or #SYMBOL
    pattern = r'[\$\#]([A-Z]{2,10})\b'
    matches = re.findall(pattern, text.upper())
    
    # Common crypto symbols
    known_symbols = {
        'BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'XRP', 'DOT', 'DOGE', 
        'AVAX', 'LUNA', 'SHIB', 'MATIC', 'CRO', 'ATOM', 'LINK',
        'UNI', 'LTC', 'ALGO', 'VET', 'ICP', 'FTT', 'MANA', 'SAND'
    }
    
    # Filter to only known symbols
    return [symbol for symbol in matches if symbol in known_symbols]


def calculate_social_sentiment_score(messages: List[Dict]) -> float:
    """Calculate basic sentiment score from social messages"""
    if not messages:
        return 0.0
        
    positive_words = ['moon', 'bullish', 'pump', 'buy', 'hodl', 'diamond', 'rocket']
    negative_words = ['dump', 'bearish', 'sell', 'crash', 'rekt', 'fud']
    
    total_score = 0
    total_weight = 0
    
    for message in messages:
        text = message.get('text', '').lower()
        
        # Calculate sentiment
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count + negative_count > 0:
            sentiment = (positive_count - negative_count) / (positive_count + negative_count)
        else:
            sentiment = 0
            
        # Weight by engagement (likes, retweets, views)
        weight = 1
        if 'like_count' in message:
            weight += message['like_count'] * 0.1
        if 'retweet_count' in message:
            weight += message['retweet_count'] * 0.2
        if 'views' in message:
            weight += message['views'] * 0.01
            
        total_score += sentiment * weight
        total_weight += weight
        
    return total_score / total_weight if total_weight > 0 else 0.0
