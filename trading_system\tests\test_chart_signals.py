"""
Unit tests for chart signal generation.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.signal_generators.chart_signals import ChartSignalGenerator, TechnicalIndicators
from src.shared.data_models import SignalType, TradeDirection


class TestTechnicalIndicators:
    """Test technical indicator calculations"""
    
    def setup_method(self):
        """Setup test data"""
        # Create sample price data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 45000
        returns = np.random.normal(0, 0.02, 100)  # 2% volatility
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        })
    
    def test_rsi_calculation(self):
        """Test RSI calculation"""
        prices = self.sample_data['close']
        rsi = TechnicalIndicators.rsi(prices, period=14)
        
        # RSI should be between 0 and 100
        assert rsi.min() >= 0
        assert rsi.max() <= 100
        
        # Should have NaN values for first 14 periods
        assert pd.isna(rsi.iloc[:13]).all()
        assert not pd.isna(rsi.iloc[14:]).any()
    
    def test_bollinger_bands(self):
        """Test Bollinger Bands calculation"""
        prices = self.sample_data['close']
        upper, middle, lower = TechnicalIndicators.bollinger_bands(prices, period=20, std=2.0)
        
        # Upper band should be above middle, middle above lower
        assert (upper >= middle).all()
        assert (middle >= lower).all()
        
        # Middle band should be the moving average
        expected_middle = prices.rolling(window=20).mean()
        pd.testing.assert_series_equal(middle, expected_middle)
    
    def test_macd_calculation(self):
        """Test MACD calculation"""
        prices = self.sample_data['close']
        macd_line, signal_line, histogram = TechnicalIndicators.macd(prices)
        
        # Histogram should be MACD line minus signal line
        expected_histogram = macd_line - signal_line
        pd.testing.assert_series_equal(histogram, expected_histogram)
        
        # Should have some non-NaN values
        assert not macd_line.isna().all()
        assert not signal_line.isna().all()


class TestChartSignalGenerator:
    """Test chart signal generation"""
    
    def setup_method(self):
        """Setup test environment"""
        self.generator = ChartSignalGenerator()
        
        # Create test data with known patterns
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
        
        # Create oversold condition (RSI < 30)
        prices = [45000] * 20 + [44000] * 10 + [43000] * 10 + [42000] * 10
        
        self.oversold_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices], 
            'close': prices,
            'volume': [5000] * 50
        })
        
        # Create overbought condition (RSI > 70)
        prices_up = [45000] + [45000 + i*100 for i in range(1, 50)]
        
        self.overbought_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices_up,
            'high': [p * 1.005 for p in prices_up],
            'low': [p * 0.995 for p in prices_up],
            'close': prices_up,
            'volume': [5000] * 50
        })
    
    def test_rsi_oversold_signal(self):
        """Test RSI oversold signal generation"""
        signals = self.generator.analyze_price_data(self.oversold_data, 'BTCUSDT')
        
        # Should generate at least one signal
        assert len(signals) > 0
        
        # Should have RSI-based signals
        rsi_signals = [s for s in signals if s.source == "RSI_Oversold"]
        assert len(rsi_signals) > 0
        
        # RSI oversold should generate BUY signals
        for signal in rsi_signals:
            assert signal.direction == TradeDirection.BUY
            assert signal.signal_type == SignalType.CHART
            assert signal.confidence > 0
    
    def test_rsi_overbought_signal(self):
        """Test RSI overbought signal generation"""
        signals = self.generator.analyze_price_data(self.overbought_data, 'BTCUSDT')
        
        # Should generate signals
        assert len(signals) > 0
        
        # Should have RSI overbought signals
        rsi_signals = [s for s in signals if s.source == "RSI_Overbought"]
        
        if rsi_signals:  # May not always trigger depending on exact RSI values
            for signal in rsi_signals:
                assert signal.direction == TradeDirection.SELL
                assert signal.signal_type == SignalType.CHART
    
    def test_volume_analysis(self):
        """Test volume spike detection"""
        # Create data with volume spike
        dates = pd.date_range(start='2024-01-01', periods=30, freq='1H')
        prices = [45000] * 25 + [46000] * 5  # Price increase
        volumes = [1000] * 25 + [10000] * 5  # Volume spike
        
        volume_spike_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': volumes
        })
        
        signals = self.generator.analyze_price_data(volume_spike_data, 'BTCUSDT')
        
        # Should detect volume breakout
        volume_signals = [s for s in signals if s.signal_type == SignalType.VOLUME]
        assert len(volume_signals) > 0
        
        # Volume breakout with price increase should be bullish
        for signal in volume_signals:
            if signal.source == "Volume_Breakout":
                assert signal.direction == TradeDirection.BUY
    
    def test_insufficient_data(self):
        """Test behavior with insufficient data"""
        # Create very small dataset
        small_data = pd.DataFrame({
            'open': [45000, 45100],
            'high': [45050, 45150],
            'low': [44950, 45050],
            'close': [45000, 45100],
            'volume': [1000, 1100]
        })
        
        signals = self.generator.analyze_price_data(small_data, 'BTCUSDT')
        
        # Should return empty list for insufficient data
        assert len(signals) == 0
    
    def test_signal_properties(self):
        """Test that generated signals have correct properties"""
        signals = self.generator.analyze_price_data(self.oversold_data, 'BTCUSDT')
        
        for signal in signals:
            # Check required properties
            assert signal.asset == 'BTCUSDT'
            assert isinstance(signal.confidence, float)
            assert 0 <= signal.confidence <= 1
            assert signal.direction in [TradeDirection.BUY, TradeDirection.SELL]
            assert signal.signal_type in [SignalType.CHART, SignalType.VOLUME]
            assert isinstance(signal.timestamp, datetime)
            assert isinstance(signal.details, dict)
            assert signal.source is not None


@pytest.fixture
def sample_price_data():
    """Fixture providing sample price data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    base_price = 45000
    returns = np.random.normal(0, 0.01, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    return pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * 1.01 for p in prices],
        'low': [p * 0.99 for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, 100)
    })


def test_signal_generation_integration(sample_price_data):
    """Integration test for signal generation"""
    generator = ChartSignalGenerator()
    signals = generator.analyze_price_data(sample_price_data, 'BTCUSDT')
    
    # Should generate some signals with realistic data
    assert isinstance(signals, list)
    
    # All signals should be valid
    for signal in signals:
        assert signal.asset == 'BTCUSDT'
        assert 0 <= signal.confidence <= 1
        assert signal.direction in [TradeDirection.BUY, TradeDirection.SELL]
