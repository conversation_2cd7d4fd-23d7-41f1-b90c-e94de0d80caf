# SysCryp Trading System - Setup Guide

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- Git
- API keys for:
  - Binance (for trading)
  - Etherscan (for on-chain data)
  - Google Gemini (for AI analysis)
  - Twitter/Telegram (optional, for social sentiment)

### Installation

1. **<PERSON><PERSON> and navigate to the project:**
```bash
cd trading_system
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your API keys and settings
```

4. **Run tests to verify installation:**
```bash
python run_tests.py
```

5. **Start the trading system:**
```bash
python src/main.py
```

6. **Launch the dashboard (in another terminal):**
```bash
python run_dashboard.py
```

## 📋 Configuration

### Environment Variables (.env)

```env
# Required API Keys
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
ETHERSCAN_API_KEY=your_etherscan_api_key
GEMINI_API_KEY=your_gemini_api_key

# Optional Social Media APIs
TWITTER_BEARER_TOKEN=your_twitter_token
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Trading Configuration
MAX_POSITION_SIZE_USD=1000
RISK_PER_TRADE_PERCENT=1.0
MAX_OPEN_POSITIONS=5
ENTRY_CONFIDENCE_THRESHOLD=0.7

# System Configuration
LOG_LEVEL=INFO
```

### Trading Parameters

- **MAX_POSITION_SIZE_USD**: Maximum USD value per position
- **RISK_PER_TRADE_PERCENT**: Percentage of portfolio to risk per trade
- **MAX_OPEN_POSITIONS**: Maximum number of concurrent positions
- **ENTRY_CONFIDENCE_THRESHOLD**: Minimum confidence score to enter trades

## 🔧 System Components

### 1. Data Harvesters
- **CEX Streamer**: Real-time Binance WebSocket data
- **On-chain Fetcher**: Ethereum blockchain analysis via Etherscan
- **Social Scraper**: Twitter and Telegram sentiment data

### 2. Signal Generators
- **Chart Signals**: RSI, Bollinger Bands, MACD, volume analysis
- **Whale Signals**: Large wallet movement detection
- **Spoof Detector**: Order book manipulation detection

### 3. AI Layer
- **Contract Analyzer**: Smart contract risk assessment using Gemini AI
- **Sentiment Analyzer**: Social media sentiment analysis

### 4. Trading Engine
- **Decision Engine**: Combines signals with regime-aware strategies
- **Regime Classifier**: Market condition detection (RANGE, BREAKOUT, TREND)
- **Risk Manager**: Position sizing and portfolio risk controls

### 5. Execution
- **Exchange Handler**: Order placement and management
- **Mock Mode**: Safe testing without real trades

### 6. Monitoring
- **Streamlit Dashboard**: Real-time system monitoring
- **Telegram Bot**: Trade alerts and notifications

## 🎯 Market Regimes

The system adapts its strategy based on detected market conditions:

| Regime | Description | Strategy Focus |
|--------|-------------|----------------|
| RANGE | Low volatility, sideways | Mean reversion |
| BREAKOUT | High momentum | Trend following |
| TREND_UP | Sustained upward movement | Buy dips |
| TREND_DOWN | Sustained downward movement | Sell rallies |
| HIGH_VOLATILITY | Extreme price swings | Risk reduction |

## 📊 Signal Types

### Chart Signals
- RSI oversold/overbought
- Bollinger Band touches
- MACD crossovers
- Volume breakouts

### Whale Signals
- Exchange inflows/outflows
- Large wallet accumulation
- Unusual transaction patterns

### Sentiment Signals
- Social media sentiment analysis
- Keyword trend detection
- Community mood assessment

## 🛡️ Risk Management

### Position Sizing
- Kelly criterion-based sizing
- Confidence-adjusted risk
- Regime-aware multipliers

### Portfolio Controls
- Maximum position limits
- Correlation constraints
- Total risk exposure limits

### Trade Management
- Automatic stop-loss orders
- Dynamic take-profit targets
- Position monitoring

## 📈 Dashboard Features

Access the dashboard at `http://localhost:8501`:

- **Portfolio Overview**: Real-time P&L and metrics
- **Signal Activity**: Live signal generation monitoring
- **Market Regime**: Current market condition analysis
- **Trade History**: Recent trade execution log
- **System Health**: Component status monitoring

## 🔔 Alerts & Notifications

### Telegram Integration
- Trade execution alerts
- High-confidence signal notifications
- Market regime changes
- System status updates

### Alert Configuration
```python
# Configure alert settings
alert_manager.configure_alerts(
    trade_alerts=True,
    signal_alerts=True,
    regime_alerts=True,
    signal_threshold=0.8
)
```

## 🧪 Testing

### Run Unit Tests
```bash
python run_tests.py
```

### Test Coverage
- Chart signal generation
- Risk management calculations
- Position sizing logic
- Portfolio state management

## 🚨 Safety Features

### Mock Trading Mode
- Default mode for testing
- No real money at risk
- Full system functionality

### Risk Limits
- Maximum position sizes
- Portfolio exposure limits
- Stop-loss automation

### Error Handling
- Graceful degradation
- Automatic recovery
- Comprehensive logging

## 📚 Usage Examples

### Basic Operation
```python
# Start the system
python src/main.py

# Monitor via dashboard
python run_dashboard.py
```

### Custom Configuration
```python
# Modify risk parameters
MAX_POSITION_SIZE_USD=500  # Reduce position size
RISK_PER_TRADE_PERCENT=0.5  # Lower risk per trade
```

## 🔍 Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Verify API keys in .env file
   - Check internet connection
   - Ensure API permissions

2. **Import Errors**
   - Install missing dependencies: `pip install -r requirements.txt`
   - Check Python version (3.10+ required)

3. **Dashboard Not Loading**
   - Ensure Streamlit is installed: `pip install streamlit`
   - Check port 8501 is available

### Logs
- System logs are output to console
- Adjust log level in .env: `LOG_LEVEL=DEBUG`

## 🔄 Updates & Maintenance

### Regular Tasks
- Monitor system performance
- Review trade results
- Update API keys as needed
- Backup configuration

### Performance Optimization
- Adjust signal weights based on performance
- Fine-tune risk parameters
- Update market regime thresholds

## 📞 Support

For issues or questions:
1. Check the logs for error messages
2. Review the troubleshooting section
3. Ensure all dependencies are installed
4. Verify API key configuration

## ⚠️ Disclaimer

This software is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Use at your own risk and never trade with money you cannot afford to lose.
