"""
Script to run the Streamlit dashboard.
"""

import subprocess
import sys
import os

def run_dashboard():
    """Run the Streamlit dashboard"""
    
    # Get the path to the dashboard app
    dashboard_path = os.path.join("src", "monitors", "dashboard_app.py")
    
    if not os.path.exists(dashboard_path):
        print(f"Dashboard app not found at {dashboard_path}")
        return
    
    print("Starting SysCryp Trading Dashboard...")
    print("Dashboard will be available at: http://localhost:8501")
    print("Press Ctrl+C to stop the dashboard")
    
    try:
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", dashboard_path,
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\nDashboard stopped.")
    except Exception as e:
        print(f"Error running dashboard: {e}")

if __name__ == "__main__":
    run_dashboard()
