# SysCryp Trading System

A sophisticated multi-signal, AI-augmented cryptocurrency trading system designed to generate alpha through systematic analysis of on-chain data, market sentiment, and technical indicators.

## 🎯 System Overview

The SysCryp Trading System combines multiple data sources and analytical approaches:

- **On-chain Analysis**: Whale movement tracking, smart contract risk assessment
- **Market Data**: Real-time CEX/DEX order book analysis, spoofing detection
- **Sentiment Analysis**: Social media sentiment extraction using AI
- **Technical Analysis**: Chart patterns, momentum indicators, volatility analysis
- **Market Regime Detection**: Adaptive strategies based on market conditions
- **Risk Management**: Portfolio-level and trade-level risk controls

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Data Sources   │    │  Signal Gen     │    │  AI Enrichment  │
│                 │    │                 │    │                 │
│ • Binance WS    │───▶│ • Chart Signals │───▶│ • Contract Risk │
│ • Etherscan     │    │ • Whale Tracker │    │ • Sentiment     │
│ • Social Media  │    │ • Spoof Detect  │    │ • LLM Analysis  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Monitoring     │    │ Decision Engine │    │ Risk Management │
│                 │    │                 │    │                 │
│ • Dashboard     │◀───│ • Regime Detect │───▶│ • Position Size │
│ • Telegram Bot  │    │ • Signal Fusion │    │ • Portfolio     │
│ • Alerts        │    │ • Strategy      │    │ • Stop Loss     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Execution     │
                       │                 │
                       │ • Order Mgmt    │
                       │ • Exchange API  │
                       │ • Trade Log     │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.10+
- API keys for:
  - Binance (trading)
  - Etherscan (on-chain data)
  - Google Gemini (AI analysis)
  - Twitter/Telegram (optional, for social sentiment)

### Installation

1. Navigate to the trading system:
```bash
cd trading_system
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment:
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

4. Run tests to verify setup:
```bash
python run_tests.py
```

5. Start the trading system:
```bash
python src/main.py
```

6. Launch the dashboard (in another terminal):
```bash
python run_dashboard.py
# Access at http://localhost:8501
```

## 📊 Features

### Signal Generation
- **Chart Intelligence**: RSI, Bollinger Bands, momentum indicators
- **Whale Tracking**: Large wallet movements, exchange flows
- **Spoofing Detection**: Order book manipulation identification
- **Volume Analysis**: Unusual volume patterns

### AI Integration
- **Smart Contract Analysis**: Automated risk assessment using Gemini AI
- **Sentiment Analysis**: Social media sentiment extraction
- **Market Regime Classification**: Adaptive strategy selection

### Risk Management
- **Position Sizing**: Kelly criterion-based sizing
- **Portfolio Risk**: Correlation limits, exposure controls
- **Trade Risk**: Stop-loss, take-profit automation

### Monitoring
- **Real-time Dashboard**: Streamlit-based system monitoring
- **Telegram Alerts**: Instant trade notifications
- **Performance Tracking**: P&L, drawdown, win rate metrics

## 🔧 Configuration

Key configuration parameters in `.env`:

```env
# Trading Risk
MAX_POSITION_SIZE_USD=1000
RISK_PER_TRADE_PERCENT=1.0
MAX_OPEN_POSITIONS=5
ENTRY_CONFIDENCE_THRESHOLD=0.7

# Signal Weights (per regime)
# Automatically adjusted based on market conditions
```

## 📈 Market Regimes

The system adapts its strategy based on detected market regimes:

- **RANGE**: Sideways market, emphasis on mean reversion
- **BREAKOUT**: High momentum, trend-following signals
- **TREND**: Sustained directional movement
- **HIGH_VOLATILITY**: Risk-off mode, reduced position sizes

## 🧪 Testing

Run the test suite:
```bash
pytest tests/
```

## 📝 License

This project is proprietary software. All rights reserved.

## ⚠️ Disclaimer

This software is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Use at your own risk.
