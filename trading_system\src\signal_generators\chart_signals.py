"""
Chart analysis signal generation using technical indicators.
Implements RSI, Bollinger Bands, momentum, and pattern recognition.
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Optional, Dict, Tuple
from datetime import datetime

try:
    import pandas_ta as ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

from ..shared.data_models import Signal, SignalType, TradeDirection
from ..shared.config import config


class TechnicalIndicators:
    """Technical indicator calculations"""
    
    @staticmethod
    def rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI (Relative Strength Index)"""
        if TA_AVAILABLE:
            return ta.rsi(prices, length=period)
        else:
            # Manual RSI calculation
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
    
    @staticmethod
    def bollinger_bands(prices: pd.Series, period: int = 20, std: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands (upper, middle, lower)"""
        if TA_AVAILABLE:
            bb = ta.bbands(prices, length=period, std=std)
            return bb[f'BBU_{period}_{std}'], bb[f'BBM_{period}_{std}'], bb[f'BBL_{period}_{std}']
        else:
            # Manual calculation
            middle = prices.rolling(window=period).mean()
            std_dev = prices.rolling(window=period).std()
            upper = middle + (std_dev * std)
            lower = middle - (std_dev * std)
            return upper, middle, lower
    
    @staticmethod
    def macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        if TA_AVAILABLE:
            macd_data = ta.macd(prices, fast=fast, slow=slow, signal=signal)
            return macd_data[f'MACD_{fast}_{slow}_{signal}'], macd_data[f'MACDs_{fast}_{slow}_{signal}'], macd_data[f'MACDh_{fast}_{slow}_{signal}']
        else:
            # Manual calculation
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line
            return macd_line, signal_line, histogram
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Calculate Stochastic Oscillator"""
        if TA_AVAILABLE:
            stoch = ta.stoch(high, low, close, k=k_period, d=d_period)
            return stoch[f'STOCHk_{k_period}_{d_period}_{d_period}'], stoch[f'STOCHd_{k_period}_{d_period}_{d_period}']
        else:
            # Manual calculation
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            return k_percent, d_percent


class ChartSignalGenerator:
    """Generates trading signals from chart analysis"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rsi_period = config.rsi_period
        self.rsi_oversold = config.rsi_oversold
        self.rsi_overbought = config.rsi_overbought
        self.bb_period = config.bollinger_period
        self.bb_std = config.bollinger_std
        
    def analyze_price_data(self, df: pd.DataFrame, asset: str) -> List[Signal]:
        """Analyze price data and generate signals"""
        signals = []
        
        if len(df) < 50:  # Need sufficient data
            return signals
            
        # Ensure we have required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            self.logger.warning(f"Missing required columns for {asset}")
            return signals
            
        try:
            # Calculate indicators
            rsi = TechnicalIndicators.rsi(df['close'], self.rsi_period)
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'], self.bb_period, self.bb_std)
            macd_line, macd_signal, macd_hist = TechnicalIndicators.macd(df['close'])
            stoch_k, stoch_d = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
            
            # Get latest values
            latest_idx = df.index[-1]
            current_price = df['close'].iloc[-1]
            current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else None
            current_bb_upper = bb_upper.iloc[-1] if not pd.isna(bb_upper.iloc[-1]) else None
            current_bb_lower = bb_lower.iloc[-1] if not pd.isna(bb_lower.iloc[-1]) else None
            current_macd = macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else None
            current_macd_signal = macd_signal.iloc[-1] if not pd.isna(macd_signal.iloc[-1]) else None
            current_stoch_k = stoch_k.iloc[-1] if not pd.isna(stoch_k.iloc[-1]) else None
            
            # RSI Signals
            if current_rsi is not None:
                if current_rsi <= self.rsi_oversold:
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=min(0.9, (self.rsi_oversold - current_rsi) / self.rsi_oversold + 0.5),
                        direction=TradeDirection.BUY,
                        source="RSI_Oversold",
                        details={
                            "rsi_value": current_rsi,
                            "threshold": self.rsi_oversold,
                            "price": current_price
                        }
                    ))
                elif current_rsi >= self.rsi_overbought:
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=min(0.9, (current_rsi - self.rsi_overbought) / (100 - self.rsi_overbought) + 0.5),
                        direction=TradeDirection.SELL,
                        source="RSI_Overbought",
                        details={
                            "rsi_value": current_rsi,
                            "threshold": self.rsi_overbought,
                            "price": current_price
                        }
                    ))
            
            # Bollinger Band Signals
            if current_bb_upper is not None and current_bb_lower is not None:
                if current_price <= current_bb_lower:
                    # Price touching lower band - potential buy
                    distance_ratio = (current_bb_lower - current_price) / current_bb_lower
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=min(0.8, distance_ratio * 10 + 0.4),
                        direction=TradeDirection.BUY,
                        source="Bollinger_Lower",
                        details={
                            "price": current_price,
                            "lower_band": current_bb_lower,
                            "distance_ratio": distance_ratio
                        }
                    ))
                elif current_price >= current_bb_upper:
                    # Price touching upper band - potential sell
                    distance_ratio = (current_price - current_bb_upper) / current_bb_upper
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=min(0.8, distance_ratio * 10 + 0.4),
                        direction=TradeDirection.SELL,
                        source="Bollinger_Upper",
                        details={
                            "price": current_price,
                            "upper_band": current_bb_upper,
                            "distance_ratio": distance_ratio
                        }
                    ))
            
            # MACD Signals
            if current_macd is not None and current_macd_signal is not None and len(macd_line) >= 2:
                prev_macd = macd_line.iloc[-2]
                prev_macd_signal = macd_signal.iloc[-2]
                
                # MACD bullish crossover
                if (prev_macd <= prev_macd_signal and current_macd > current_macd_signal):
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=0.7,
                        direction=TradeDirection.BUY,
                        source="MACD_Bullish_Cross",
                        details={
                            "macd": current_macd,
                            "signal": current_macd_signal,
                            "price": current_price
                        }
                    ))
                # MACD bearish crossover
                elif (prev_macd >= prev_macd_signal and current_macd < current_macd_signal):
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=0.7,
                        direction=TradeDirection.SELL,
                        source="MACD_Bearish_Cross",
                        details={
                            "macd": current_macd,
                            "signal": current_macd_signal,
                            "price": current_price
                        }
                    ))
            
            # Stochastic Signals
            if current_stoch_k is not None:
                if current_stoch_k <= 20:  # Oversold
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=0.6,
                        direction=TradeDirection.BUY,
                        source="Stochastic_Oversold",
                        details={
                            "stoch_k": current_stoch_k,
                            "price": current_price
                        }
                    ))
                elif current_stoch_k >= 80:  # Overbought
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.CHART,
                        confidence=0.6,
                        direction=TradeDirection.SELL,
                        source="Stochastic_Overbought",
                        details={
                            "stoch_k": current_stoch_k,
                            "price": current_price
                        }
                    ))
            
            # Volume analysis
            volume_signals = self._analyze_volume(df, asset)
            signals.extend(volume_signals)
            
        except Exception as e:
            self.logger.error(f"Error analyzing chart data for {asset}: {e}")
            
        return signals
    
    def _analyze_volume(self, df: pd.DataFrame, asset: str) -> List[Signal]:
        """Analyze volume patterns for additional signals"""
        signals = []
        
        if len(df) < 20:
            return signals
            
        try:
            # Calculate volume moving average
            volume_ma = df['volume'].rolling(window=20).mean()
            current_volume = df['volume'].iloc[-1]
            avg_volume = volume_ma.iloc[-1]
            
            if pd.isna(avg_volume):
                return signals
                
            # High volume breakout
            if current_volume > avg_volume * 2:  # Volume spike
                price_change = (df['close'].iloc[-1] - df['close'].iloc[-2]) / df['close'].iloc[-2]
                
                if abs(price_change) > 0.02:  # Significant price movement
                    direction = TradeDirection.BUY if price_change > 0 else TradeDirection.SELL
                    confidence = min(0.8, abs(price_change) * 10 + 0.3)
                    
                    signals.append(Signal(
                        asset=asset,
                        signal_type=SignalType.VOLUME,
                        confidence=confidence,
                        direction=direction,
                        source="Volume_Breakout",
                        details={
                            "volume": current_volume,
                            "avg_volume": avg_volume,
                            "volume_ratio": current_volume / avg_volume,
                            "price_change": price_change,
                            "price": df['close'].iloc[-1]
                        }
                    ))
                    
        except Exception as e:
            self.logger.error(f"Error analyzing volume for {asset}: {e}")
            
        return signals
