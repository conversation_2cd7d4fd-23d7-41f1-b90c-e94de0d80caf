"""
Unit tests for risk management system.
"""

import pytest
from datetime import datetime

from src.engine.risk_manager import RiskManager, PositionSizer
from src.shared.data_models import (
    ProposedTrade, TradeDirection, MarketRegime, ExecutedTrade, TradeStatus
)


class TestPositionSizer:
    """Test position sizing calculations"""
    
    def setup_method(self):
        """Setup test environment"""
        self.position_sizer = PositionSizer()
    
    def test_basic_position_sizing(self):
        """Test basic position size calculation"""
        proposed_trade = ProposedTrade(
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            confidence=0.8,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        portfolio_value = 10000.0
        current_price = 45000.0
        stop_loss_price = 44100.0  # 2% stop loss
        
        quantity, risk_amount = self.position_sizer.calculate_position_size(
            proposed_trade, portfolio_value, current_price, stop_loss_price
        )
        
        # Should return positive values
        assert quantity > 0
        assert risk_amount > 0
        
        # Risk amount should be reasonable (not more than 5% of portfolio)
        assert risk_amount <= portfolio_value * 0.05
    
    def test_confidence_adjustment(self):
        """Test that higher confidence leads to larger position sizes"""
        base_trade = ProposedTrade(
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            confidence=0.5,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        high_confidence_trade = ProposedTrade(
            asset="BTCUSDT", 
            direction=TradeDirection.BUY,
            confidence=0.9,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        portfolio_value = 10000.0
        current_price = 45000.0
        stop_loss_price = 44100.0
        
        base_qty, base_risk = self.position_sizer.calculate_position_size(
            base_trade, portfolio_value, current_price, stop_loss_price
        )
        
        high_qty, high_risk = self.position_sizer.calculate_position_size(
            high_confidence_trade, portfolio_value, current_price, stop_loss_price
        )
        
        # Higher confidence should lead to larger position
        assert high_risk >= base_risk
    
    def test_regime_risk_adjustment(self):
        """Test risk adjustment based on market regime"""
        volatile_trade = ProposedTrade(
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            confidence=0.8,
            signals=[],
            market_regime=MarketRegime.HIGH_VOLATILITY
        )
        
        range_trade = ProposedTrade(
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            confidence=0.8,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        portfolio_value = 10000.0
        current_price = 45000.0
        stop_loss_price = 44100.0
        
        volatile_qty, volatile_risk = self.position_sizer.calculate_position_size(
            volatile_trade, portfolio_value, current_price, stop_loss_price
        )
        
        range_qty, range_risk = self.position_sizer.calculate_position_size(
            range_trade, portfolio_value, current_price, stop_loss_price
        )
        
        # High volatility regime should have lower risk
        assert volatile_risk < range_risk


class TestRiskManager:
    """Test risk manager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.risk_manager = RiskManager()
    
    @pytest.mark.asyncio
    async def test_validate_and_size_basic(self):
        """Test basic trade validation and sizing"""
        proposed_trade = ProposedTrade(
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            confidence=0.8,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        sized_order = await self.risk_manager.validate_and_size(proposed_trade)
        
        # Should return a valid sized order
        assert sized_order is not None
        assert sized_order.asset == "BTCUSDT"
        assert sized_order.direction == TradeDirection.BUY
        assert sized_order.quantity > 0
        assert sized_order.risk_amount > 0
        assert sized_order.stop_loss is not None
        assert sized_order.take_profit is not None
    
    @pytest.mark.asyncio
    async def test_position_limit_enforcement(self):
        """Test that position limits are enforced"""
        # Fill up to maximum positions
        for i in range(5):  # Assuming max_open_positions = 5
            executed_trade = ExecutedTrade(
                trade_id=f"test_{i}",
                asset=f"TEST{i}USDT",
                direction=TradeDirection.BUY,
                quantity=0.1,
                entry_price=45000.0,
                status=TradeStatus.FILLED
            )
            self.risk_manager.update_portfolio_state(executed_trade)
        
        # Try to add one more position
        proposed_trade = ProposedTrade(
            asset="NEWUSDT",
            direction=TradeDirection.BUY,
            confidence=0.8,
            signals=[],
            market_regime=MarketRegime.RANGE
        )
        
        sized_order = await self.risk_manager.validate_and_size(proposed_trade)
        
        # Should be rejected due to position limit
        assert sized_order is None
    
    def test_portfolio_state_update(self):
        """Test portfolio state updates"""
        initial_cash = self.risk_manager.portfolio_state.available_cash
        initial_positions = len(self.risk_manager.portfolio_state.open_positions)
        
        executed_trade = ExecutedTrade(
            trade_id="test_trade",
            asset="BTCUSDT",
            direction=TradeDirection.BUY,
            quantity=0.1,
            entry_price=45000.0,
            status=TradeStatus.FILLED
        )
        
        self.risk_manager.update_portfolio_state(executed_trade)
        
        # Should have one more position
        assert len(self.risk_manager.portfolio_state.open_positions) == initial_positions + 1
        
        # Available cash should decrease for BUY order
        expected_cash = initial_cash - (executed_trade.quantity * executed_trade.entry_price)
        assert abs(self.risk_manager.portfolio_state.available_cash - expected_cash) < 0.01
    
    def test_stop_loss_calculation(self):
        """Test stop loss price calculation"""
        current_price = 45000.0
        
        # Test BUY order stop loss
        buy_stop = self.risk_manager._calculate_stop_loss(current_price, TradeDirection.BUY)
        assert buy_stop < current_price  # Stop loss should be below entry for BUY
        assert buy_stop == current_price * 0.98  # 2% stop loss
        
        # Test SELL order stop loss
        sell_stop = self.risk_manager._calculate_stop_loss(current_price, TradeDirection.SELL)
        assert sell_stop > current_price  # Stop loss should be above entry for SELL
        assert sell_stop == current_price * 1.02  # 2% stop loss
    
    def test_take_profit_calculation(self):
        """Test take profit calculation"""
        current_price = 45000.0
        
        # Test with different confidence levels
        low_confidence_tp = self.risk_manager._calculate_take_profit(
            current_price, TradeDirection.BUY, 0.5
        )
        high_confidence_tp = self.risk_manager._calculate_take_profit(
            current_price, TradeDirection.BUY, 0.9
        )
        
        # Higher confidence should have higher take profit target
        assert high_confidence_tp > low_confidence_tp
        
        # Both should be above current price for BUY orders
        assert low_confidence_tp > current_price
        assert high_confidence_tp > current_price
    
    def test_portfolio_summary(self):
        """Test portfolio summary generation"""
        summary = self.risk_manager.get_portfolio_summary()
        
        # Should contain expected keys
        expected_keys = [
            'total_value', 'available_cash', 'open_positions', 
            'total_pnl', 'risk_utilization'
        ]
        
        for key in expected_keys:
            assert key in summary
        
        # Values should be reasonable
        assert summary['total_value'] > 0
        assert summary['available_cash'] >= 0
        assert summary['open_positions'] >= 0
        assert 0 <= summary['risk_utilization'] <= 1


@pytest.fixture
def sample_proposed_trade():
    """Fixture providing a sample proposed trade"""
    return ProposedTrade(
        asset="BTCUSDT",
        direction=TradeDirection.BUY,
        confidence=0.75,
        signals=[],
        market_regime=MarketRegime.RANGE,
        reasoning="Test trade for unit testing"
    )


@pytest.mark.asyncio
async def test_risk_manager_integration(sample_proposed_trade):
    """Integration test for risk manager"""
    risk_manager = RiskManager()
    
    # Test full flow
    sized_order = await risk_manager.validate_and_size(sample_proposed_trade)
    
    assert sized_order is not None
    
    # Simulate execution
    executed_trade = ExecutedTrade(
        trade_id="integration_test",
        asset=sized_order.asset,
        direction=sized_order.direction,
        quantity=sized_order.quantity,
        entry_price=sized_order.price,
        status=TradeStatus.FILLED
    )
    
    # Update portfolio
    risk_manager.update_portfolio_state(executed_trade)
    
    # Check portfolio state
    summary = risk_manager.get_portfolio_summary()
    assert summary['open_positions'] == 1
    assert summary['total_value'] > 0


def test_edge_cases():
    """Test edge cases and error conditions"""
    risk_manager = RiskManager()
    
    # Test with zero confidence
    zero_confidence_trade = ProposedTrade(
        asset="BTCUSDT",
        direction=TradeDirection.BUY,
        confidence=0.0,
        signals=[],
        market_regime=MarketRegime.RANGE
    )
    
    # Should still work but with minimal position size
    quantity, risk = risk_manager.position_sizer.calculate_position_size(
        zero_confidence_trade, 10000.0, 45000.0, 44100.0
    )
    
    assert quantity > 0
    assert risk > 0
