"""
Sentiment analysis module for social media data.
Uses both rule-based and AI-powered sentiment analysis.
"""

import asyncio
import logging
import re
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False

from .gemini_client import GeminiClient
from ..shared.data_models import SentimentAnalysis
from ..shared.config import config


class RuleBasedSentimentAnalyzer:
    """Rule-based sentiment analysis using keyword matching"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Crypto-specific sentiment keywords
        self.positive_keywords = {
            'moon', 'bullish', 'pump', 'buy', 'hodl', 'diamond', 'rocket', 'lambo',
            'breakout', 'rally', 'surge', 'gains', 'profit', 'green', 'up', 'rise',
            'accumulate', 'long', 'support', 'bounce', 'recovery', 'strong', 'solid',
            'gem', 'alpha', 'opportunity', 'undervalued', 'potential', 'growth'
        }
        
        self.negative_keywords = {
            'dump', 'bearish', 'sell', 'crash', 'rekt', 'fud', 'scam', 'rug',
            'drop', 'fall', 'down', 'red', 'loss', 'bear', 'short', 'resistance',
            'reject', 'weak', 'overvalued', 'bubble', 'correction', 'decline',
            'panic', 'fear', 'liquidation', 'capitulation', 'dead', 'worthless'
        }
        
        # Intensity modifiers
        self.intensifiers = {
            'very', 'extremely', 'super', 'massive', 'huge', 'insane', 'crazy',
            'absolutely', 'totally', 'completely', 'definitely', 'seriously'
        }
        
        self.diminishers = {
            'slightly', 'somewhat', 'maybe', 'possibly', 'perhaps', 'might',
            'could', 'little', 'bit', 'small', 'minor'
        }
    
    def analyze_text(self, text: str) -> Tuple[float, float]:
        """Analyze sentiment of text. Returns (sentiment_score, confidence)"""
        if not text:
            return 0.0, 0.0
        
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        positive_count = 0
        negative_count = 0
        intensity_modifier = 1.0
        
        for i, word in enumerate(words):
            # Check for intensity modifiers
            if word in self.intensifiers:
                intensity_modifier = 1.5
                continue
            elif word in self.diminishers:
                intensity_modifier = 0.5
                continue
            
            # Count sentiment words
            if word in self.positive_keywords:
                positive_count += intensity_modifier
            elif word in self.negative_keywords:
                negative_count += intensity_modifier
            
            # Reset intensity modifier
            intensity_modifier = 1.0
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return 0.0, 0.0
        
        # Calculate sentiment score (-1 to 1)
        sentiment_score = (positive_count - negative_count) / total_sentiment_words
        
        # Calculate confidence based on number of sentiment words
        confidence = min(1.0, total_sentiment_words / 5.0)  # Max confidence at 5+ sentiment words
        
        return sentiment_score, confidence


class SentimentAnalysisService:
    """Main sentiment analysis service combining multiple approaches"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rule_based = RuleBasedSentimentAnalyzer()
        self.gemini_client = GeminiClient()
    
    async def analyze_social_data(self, messages: List[Dict], asset: str) -> SentimentAnalysis:
        """Analyze sentiment from social media messages"""
        
        if not messages:
            return SentimentAnalysis(
                asset=asset,
                sentiment_score=0.0,
                confidence=0.0,
                source_count=0
            )
        
        # Filter messages relevant to the asset
        relevant_messages = self._filter_relevant_messages(messages, asset)
        
        if not relevant_messages:
            return SentimentAnalysis(
                asset=asset,
                sentiment_score=0.0,
                confidence=0.0,
                source_count=0
            )
        
        # Extract text from messages
        texts = [msg.get('text', '') for msg in relevant_messages]
        
        # Analyze using rule-based method
        sentiments = []
        confidences = []
        
        for text in texts:
            sentiment, confidence = self.rule_based.analyze_text(text)
            if confidence > 0:
                sentiments.append(sentiment)
                confidences.append(confidence)
        
        # AI analysis for batch processing
        if len(texts) > 5 and self.gemini_client.initialized:
            try:
                ai_sentiment, ai_confidence = await self._ai_batch_analysis(texts[:10])
                if ai_confidence > 0:
                    sentiments.append(ai_sentiment)
                    confidences.append(ai_confidence * 1.2)  # Weight AI analysis higher
            except Exception as e:
                self.logger.error(f"AI sentiment analysis failed: {e}")
        
        # Combine results
        if sentiments:
            # Weighted average
            weighted_sentiment = sum(s * c for s, c in zip(sentiments, confidences)) / sum(confidences)
            avg_confidence = sum(confidences) / len(confidences)
        else:
            weighted_sentiment = 0.0
            avg_confidence = 0.0
        
        # Extract keywords
        keywords = self._extract_keywords(texts)
        
        return SentimentAnalysis(
            asset=asset,
            sentiment_score=max(-1.0, min(1.0, weighted_sentiment)),  # Clamp to [-1, 1]
            confidence=min(1.0, avg_confidence),
            source_count=len(relevant_messages),
            keywords=keywords[:10]  # Top 10 keywords
        )
    
    async def _ai_batch_analysis(self, texts: List[str]) -> Tuple[float, float]:
        """AI-powered sentiment analysis for batch of texts"""
        if not self.gemini_client.initialized:
            return 0.0, 0.0
        
        prompt = f"""
Analyze the sentiment of these cryptocurrency-related social media posts.
Rate the overall sentiment from -1 (very bearish) to +1 (very bullish).

Posts:
{chr(10).join([f"{i+1}. {text}" for i, text in enumerate(texts)])}

Respond with just two numbers separated by a comma:
<sentiment_score>,<confidence>

Where sentiment_score is from -1 to 1, and confidence is from 0 to 1.
"""
        
        try:
            response = await self.gemini_client.generate_content(prompt)
            
            # Parse response
            parts = response.strip().split(',')
            if len(parts) >= 2:
                sentiment = float(parts[0].strip())
                confidence = float(parts[1].strip())
                return max(-1, min(1, sentiment)), max(0, min(1, confidence))
                
        except Exception as e:
            self.logger.error(f"AI sentiment parsing failed: {e}")
        
        return 0.0, 0.0
    
    def _filter_relevant_messages(self, messages: List[Dict], asset: str) -> List[Dict]:
        """Filter messages relevant to the asset"""
        relevant = []
        asset_lower = asset.lower()
        
        for msg in messages:
            text = msg.get('text', '').lower()
            
            # Check if message mentions the asset
            if (asset_lower in text or 
                f"${asset_lower}" in text or 
                f"#{asset_lower}" in text):
                relevant.append(msg)
        
        return relevant
    
    def _extract_keywords(self, texts: List[str]) -> List[str]:
        """Extract key sentiment-related words from texts"""
        all_words = []
        
        for text in texts:
            words = re.findall(r'\b\w+\b', text.lower())
            
            # Filter for sentiment and crypto-related words
            relevant_words = [
                word for word in words 
                if (word in self.rule_based.positive_keywords or 
                    word in self.rule_based.negative_keywords or
                    len(word) > 3)  # Include longer words that might be relevant
            ]
            all_words.extend(relevant_words)
        
        # Count word frequency
        word_counts = Counter(all_words)
        
        # Return most common words
        return [word for word, count in word_counts.most_common(20)]


class SentimentAggregator:
    """Aggregates sentiment data over time"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sentiment_history: Dict[str, List[SentimentAnalysis]] = {}
        
    def add_sentiment_analysis(self, analysis: SentimentAnalysis):
        """Add new sentiment analysis"""
        asset = analysis.asset.upper()
        
        if asset not in self.sentiment_history:
            self.sentiment_history[asset] = []
        
        self.sentiment_history[asset].append(analysis)
        
        # Keep only recent data (last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.sentiment_history[asset] = [
            s for s in self.sentiment_history[asset]
            if s.timestamp > cutoff_time
        ]
    
    def get_sentiment_trend(self, asset: str, hours_back: int = 6) -> Dict:
        """Get sentiment trend for an asset"""
        asset = asset.upper()
        
        if asset not in self.sentiment_history:
            return {}
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
        recent_sentiments = [
            s for s in self.sentiment_history[asset]
            if s.timestamp > cutoff_time
        ]
        
        if not recent_sentiments:
            return {}
        
        # Calculate trend metrics
        scores = [s.sentiment_score for s in recent_sentiments]
        confidences = [s.confidence for s in recent_sentiments]
        
        return {
            "current_sentiment": scores[-1] if scores else 0.0,
            "average_sentiment": sum(scores) / len(scores),
            "sentiment_change": scores[-1] - scores[0] if len(scores) > 1 else 0.0,
            "average_confidence": sum(confidences) / len(confidences),
            "sample_count": len(recent_sentiments),
            "time_span_hours": hours_back
        }
